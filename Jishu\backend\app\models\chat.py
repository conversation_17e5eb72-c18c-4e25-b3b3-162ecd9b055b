from app import db
from datetime import datetime

class ChatSession(db.Model):
    __tablename__ = 'chat_sessions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>ey('users.id'), nullable=False)
    title = db.Column(db.String(255), default='New Chat')
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('chat_sessions', lazy='dynamic'))
    subject = db.relationship('Subject')
    messages = db.relationship('ChatMessage', backref='session', lazy='dynamic', cascade='all, delete-orphan')

    __table_args__ = (
        db.Index('idx_user_id', 'user_id'),
        db.Index('idx_subject_id', 'subject_id'),
    )

    def __repr__(self):
        return f'<ChatSession {self.id} User:{self.user_id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'subject_id': self.subject_id,
            'subject_name': self.subject.name if self.subject else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'message_count': self.messages.count()
        }

class ChatMessage(db.Model):
    __tablename__ = 'chat_messages'

    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_bot = db.Column(db.Boolean, default=False)  # TRUE if message is from bot, FALSE if from user
    message = db.Column(db.Text, nullable=False)
    related_question_id = db.Column(db.Integer, db.ForeignKey('questions_with_options.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User')
    related_question = db.relationship('QuestionWithOptions')

    __table_args__ = (
        db.Index('idx_session_id', 'session_id'),
        db.Index('idx_created_at', 'created_at'),
    )

    def __repr__(self):
        sender = "Bot" if self.is_bot else f"User:{self.user_id}"
        return f'<ChatMessage {self.id} Session:{self.session_id} From:{sender}>'

    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'user_id': self.user_id,
            'user_name': self.user.name if self.user and not self.is_bot else None,
            'user_avatar': self.user.icon if self.user and not self.is_bot else None,
            'is_bot': self.is_bot,
            'message': self.message,
            'related_question_id': self.related_question_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
