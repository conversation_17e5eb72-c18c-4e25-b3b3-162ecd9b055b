import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, ScrollView } from 'react-native';
import { API_URL } from '../config/index';

const ConnectionTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message, isSuccess = true) => {
    setTestResults(prev => [...prev, { message, isSuccess, timestamp: new Date().toISOString() }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testConnection = async () => {
    setIsLoading(true);
    clearResults();

    // Log the API URL
    addResult(`Testing connection to: ${API_URL}`, true);

    try {
      // Test categories endpoint
      addResult('Testing /api/exams/categories endpoint...', true);
      const categoriesResponse = await fetch(`${API_URL}/api/exams/categories`);

      if (categoriesResponse.ok) {
        const data = await categoriesResponse.json();
        addResult(`✅ SUCCESS: Received ${data.length} exam categories`, true);
      } else {
        addResult(`❌ ERROR: Status ${categoriesResponse.status} - ${categoriesResponse.statusText}`, false);
      }

      // Test user exams endpoint
      addResult('Testing /api/user/exams endpoint...', true);
      const examsResponse = await fetch(`${API_URL}/api/user/exams`);

      if (examsResponse.ok) {
        const data = await examsResponse.json();
        addResult(`✅ SUCCESS: Received ${data.length} user exams`, true);
      } else {
        addResult(`❌ ERROR: Status ${examsResponse.status} - ${examsResponse.statusText}`, false);
      }

      // Test auth endpoint
      addResult('Testing /api/auth/request-otp endpoint...', true);
      const authResponse = await fetch(`${API_URL}/api/auth/request-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mobile_number: '9876543210' }),
      });

      if (authResponse.ok) {
        const data = await authResponse.json();
        addResult(`✅ SUCCESS: Auth endpoint working`, true);
      } else {
        addResult(`❌ ERROR: Status ${authResponse.status} - ${authResponse.statusText}`, false);
      }

    } catch (error) {
      addResult(`❌ ERROR: ${error.message}`, false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Connection Test</Text>
      <Text style={styles.subtitle}>Current API URL: {API_URL}</Text>

      <View style={styles.buttonContainer}>
        <Button
          title={isLoading ? "Testing..." : "Test Connection"}
          onPress={testConnection}
          disabled={isLoading}
        />
        <Button
          title="Clear Results"
          onPress={clearResults}
          disabled={isLoading || testResults.length === 0}
        />
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text
            key={index}
            style={[
              styles.resultText,
              result.isSuccess ? styles.successText : styles.errorText
            ]}
          >
            {result.message}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 16,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  resultsContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 8,
  },
  resultText: {
    fontSize: 14,
    marginBottom: 4,
  },
  successText: {
    color: 'green',
  },
  errorText: {
    color: 'red',
  },
});

export default ConnectionTest;
