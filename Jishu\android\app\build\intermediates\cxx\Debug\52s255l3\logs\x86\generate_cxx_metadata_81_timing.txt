# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 32ms
  [gap of 11ms]
generate_cxx_metadata completed in 59ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 20ms
generate_cxx_metadata completed in 32ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 20ms
  [gap of 26ms]
generate_cxx_metadata completed in 56ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 16ms
generate_cxx_metadata completed in 29ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 10ms
  [gap of 19ms]
generate_cxx_metadata completed in 46ms

