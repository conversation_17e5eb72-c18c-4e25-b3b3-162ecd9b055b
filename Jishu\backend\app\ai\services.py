# from langchain.document_loaders import <PERSON>y<PERSON><PERSON>oader, DirectoryLoader
# from langchain.text_splitter import RecursiveCharacterTextSplitter
# from langchain.embeddings import HuggingFaceEmbeddings, OpenAIEmbeddings
# from langchain.vectorstores import Chroma
# from langchain.chains import ConversationalR<PERSON>rie<PERSON><PERSON>hain
# from langchain.chat_models import ChatOpenAI
# from langchain.prompts import PromptTemplate
# from langchain.memory import ConversationBufferMemory
# from dotenv import load_dotenv
# import os
# import json
# import glob
# from app import db
# from app.models.exam import QuestionWithOptions
# from app.models.user import User
# from app.models.exam import Subject, ExamCategory

# # Load environment variables
# load_dotenv()

# class AIService:
#     def __init__(self):
#         # Initialize with environment variables
#         openai_api_key = os.getenv("OPENAI_API_KEY")

#         # Use OpenAI embeddings if API key is available, otherwise fallback to HuggingFace
#         if openai_api_key:
#             self.embeddings = OpenAIEmbeddings(openai_api_key=openai_api_key)
#             self.llm = ChatOpenAI(
#                 temperature=0.7,
#                 model_name="gpt-3.5-turbo",
#                 openai_api_key=openai_api_key
#             )
#         else:
#             print("Warning: No OpenAI API key found. Using HuggingFace embeddings as fallback.")
#             self.embeddings = HuggingFaceEmbeddings()
#             self.llm = ChatOpenAI(
#                 temperature=0.7,
#                 model_name="gpt-3.5-turbo"
#             )

#         # Initialize vector store and chat chain
#         self.vector_store = None
#         self.chat_chain = None

#         # Create persistent directory for vector store
#         self.persist_directory = "./chroma_db"

#         # Try to load existing vector store if it exists
#         try:
#             if os.path.exists(self.persist_directory):
#                 print("Loading existing vector store...")
#                 self.vector_store = Chroma(
#                     persist_directory=self.persist_directory,
#                     embedding_function=self.embeddings
#                 )

#                 # Initialize chat chain with existing vector store
#                 self.initialize_chat_chain()
#                 print("Existing vector store loaded successfully.")
#         except Exception as e:
#             print(f"Error loading existing vector store: {e}")
#             self.vector_store = None

#     def initialize_chat_chain(self):
#         """Initialize the conversational retrieval chain with the vector store"""
#         if self.vector_store:
#             memory = ConversationBufferMemory(
#                 memory_key="chat_history",
#                 return_messages=True
#             )

#             self.chat_chain = ConversationalRetrievalChain.from_llm(
#                 llm=self.llm,
#                 retriever=self.vector_store.as_retriever(search_kwargs={"k": 5}),
#                 memory=memory,
#                 return_source_documents=True
#             )

#     def process_pdf(self, pdf_path):
#         """Process a single PDF file and add it to the vector store"""
#         # Load PDF
#         loader = PyPDFLoader(pdf_path)
#         documents = loader.load()

#         # Split text into chunks
#         text_splitter = RecursiveCharacterTextSplitter(
#             chunk_size=1000,
#             chunk_overlap=200
#         )
#         chunks = text_splitter.split_documents(documents)

#         # Create or update vector store
#         if self.vector_store is None:
#             # Create new vector store
#             self.vector_store = Chroma.from_documents(
#                 documents=chunks,
#                 embedding=self.embeddings,
#                 persist_directory=self.persist_directory
#             )
#             # Initialize chat chain
#             self.initialize_chat_chain()
#         else:
#             # Add documents to existing vector store
#             self.vector_store.add_documents(chunks)

#         # Persist the vector store
#         if hasattr(self.vector_store, '_persist'):
#             self.vector_store._persist()

#         return len(chunks)

#     def process_pdf_directory(self, directory_path):
#         """Process all PDF files in a directory and add them to the vector store"""
#         # Check if directory exists
#         if not os.path.exists(directory_path):
#             raise ValueError(f"Directory {directory_path} does not exist")

#         # Get all PDF files in the directory
#         pdf_files = glob.glob(os.path.join(directory_path, "*.pdf"))

#         if not pdf_files:
#             raise ValueError(f"No PDF files found in {directory_path}")

#         total_chunks = 0

#         # Process each PDF file
#         for pdf_file in pdf_files:
#             print(f"Processing {pdf_file}...")
#             chunks = self.process_pdf(pdf_file)
#             total_chunks += chunks
#             print(f"Added {chunks} chunks from {pdf_file}")

#         return total_chunks

#     def get_chat_response(self, question, chat_history=[]):
#         """Get a response from the chatbot using both general knowledge and PDF content"""
#         if not self.chat_chain:
#             # If no vector store is available, use general knowledge only
#             print("Warning: No vector store available. Using general knowledge only.")

#             # Create a prompt that includes the chat history
#             chat_history_text = ""
#             if chat_history:
#                 for entry in chat_history:
#                     if isinstance(entry, dict):
#                         role = entry.get('role', 'user' if entry.get('isUser', False) else 'assistant')
#                         content = entry.get('content', entry.get('text', ''))
#                         chat_history_text += f"{role.capitalize()}: {content}\n"
#                     else:
#                         chat_history_text += f"{entry}\n"

#             # General knowledge prompt
#             general_prompt = f"""You are an AI study assistant for exam preparation.
#             You help students prepare for exams like NEET, JEE, and other competitive exams.

#             Previous conversation:
#             {chat_history_text}

#             User: {question}

#             Assistant: """

#             # Get response from LLM
#             response = self.llm.predict(general_prompt)
#             return response

#         # If vector store is available, use both general knowledge and PDF content
#         try:
#             # Format chat history for the chain
#             formatted_history = []
#             for entry in chat_history:
#                 if isinstance(entry, dict):
#                     role = "human" if entry.get('isUser', False) else "ai"
#                     content = entry.get('content', entry.get('text', ''))
#                     formatted_history.append((role, content))
#                 else:
#                     formatted_history.append(entry)

#             # Get response from the chain
#             response = self.chat_chain({
#                 "question": question,
#                 "chat_history": formatted_history
#             })

#             return response["answer"]
#         except Exception as e:
#             print(f"Error getting chat response: {e}")
#             # Fallback to general knowledge
#             return self.get_general_knowledge_response(question, chat_history)

#     def get_general_knowledge_response(self, question, chat_history=[]):
#         """Fallback method to get a response using only general knowledge"""
#         # Create a prompt that includes the chat history
#         chat_history_text = ""
#         if chat_history:
#             for entry in chat_history:
#                 if isinstance(entry, dict):
#                     role = entry.get('role', 'user' if entry.get('isUser', False) else 'assistant')
#                     content = entry.get('content', entry.get('text', ''))
#                     chat_history_text += f"{role.capitalize()}: {content}\n"
#                 else:
#                     chat_history_text += f"{entry}\n"

#         # General knowledge prompt
#         general_prompt = f"""You are an AI study assistant for exam preparation.
#         You help students prepare for exams like NEET, JEE, and other competitive exams.

#         Previous conversation:
#         {chat_history_text}

#         User: {question}

#         Assistant: """

#         # Get response from LLM
#         response = self.llm.predict(general_prompt)
#         return response

#     def generate_mcq(self, topic, num_questions=5):
#         """Generate MCQ questions based on a topic"""
#         if not self.vector_store:
#             raise ValueError("PDF must be processed first")

#         mcq_prompt = PromptTemplate(
#             template="""Based on the following context, generate {num_questions} multiple choice questions about {topic}.
#             Each question should have:
#             1. The question text
#             2. Four options (A, B, C, D)
#             3. The correct answer (must be one of: A, B, C, D)
#             4. A detailed explanation
#             5. Difficulty level (easy, medium, or hard)

#             Topic: {topic}
#             Context: {context}

#             Return the response as a valid JSON array where each object has the exact fields:
#             [
#                 {{
#                     "question": "question text",
#                     "options": ["option A", "option B", "option C", "option D"],
#                     "correct_answer": "A",
#                     "explanation": "explanation text",
#                     "difficulty": "medium"
#                 }},
#                 ...
#             ]

#             Ensure the JSON is properly formatted and valid.
#             """,
#             input_variables=["topic", "num_questions", "context"]
#         )

#         # Get relevant context from vector store
#         relevant_docs = self.vector_store.similarity_search(topic, k=5)
#         context = " ".join([doc.page_content for doc in relevant_docs])

#         # Generate MCQs using the LLM
#         try:
#             mcq_generation = self.llm.predict(
#                 mcq_prompt.format(
#                     topic=topic,
#                     num_questions=num_questions,
#                     context=context
#                 )
#             )

#             # Validate JSON format
#             json.loads(mcq_generation)

#             return mcq_generation
#         except json.JSONDecodeError:
#             # If JSON is invalid, try again with a more explicit prompt
#             fallback_prompt = PromptTemplate(
#                 template="""Generate {num_questions} multiple choice questions about {topic}.
#                 Each question must have exactly 4 options labeled A, B, C, and D.

#                 Return ONLY a valid JSON array with this exact structure:
#                 [
#                     {{
#                         "question": "What is...",
#                         "options": ["A. First option", "B. Second option", "C. Third option", "D. Fourth option"],
#                         "correct_answer": "A",
#                         "explanation": "Explanation text",
#                         "difficulty": "medium"
#                     }}
#                 ]

#                 Do not include any text before or after the JSON array.
#                 """,
#                 input_variables=["topic", "num_questions"]
#             )

#             mcq_generation = self.llm.predict(
#                 fallback_prompt.format(
#                     topic=topic,
#                     num_questions=num_questions
#                 )
#             )

#             return mcq_generation

#     def generate_and_store_mcq(self, topic, subject_id, user_id, num_questions=5):
#         """Generate MCQ questions and store them in the database"""
#         # Get subject and category information
#         subject = Subject.query.get(subject_id)
#         if not subject:
#             raise ValueError("Invalid subject ID")

#         # Generate MCQs
#         try:
#             # Try to use vector store if available
#             if self.vector_store:
#                 mcq_json = self.generate_mcq(topic, num_questions)
#             else:
#                 # Fallback to general knowledge if no vector store
#                 mcq_json = self.generate_general_mcq(topic, subject.name, num_questions)

#             # Parse the response as JSON
#             questions_data = json.loads(mcq_json)
#             stored_questions = []

#             for q_data in questions_data:
#                 # Create new question in database
#                 question = QuestionWithOptions(
#                     user_id=user_id,
#                     subject_id=subject_id,
#                     exam_category_id=subject.category_id,
#                     text=q_data['question'],
#                     difficulty=q_data.get('difficulty', 'medium'),
#                     option_a=q_data['options'][0],
#                     option_b=q_data['options'][1],
#                     option_c=q_data['options'][2],
#                     option_d=q_data['options'][3],
#                     correct_option=q_data['correct_answer'],
#                     explanation=q_data['explanation'],
#                     is_ai_generated=True
#                 )
#                 db.session.add(question)
#                 stored_questions.append(question)

#             db.session.commit()
#             return stored_questions

#         except json.JSONDecodeError as e:
#             print(f"JSON decode error: {e}")
#             raise ValueError(f"Failed to parse AI response as JSON: {e}")
#         except Exception as e:
#             print(f"Error generating MCQs: {e}")
#             db.session.rollback()
#             raise e

#     def generate_general_mcq(self, topic, subject_name, num_questions=5):
#         """Generate MCQ questions without using vector store (fallback method)"""
#         # Create a prompt for generating MCQs based on general knowledge
#         general_mcq_prompt = PromptTemplate(
#             template="""Generate {num_questions} multiple choice questions about {topic} for {subject_name} subject.
#             Each question must have:
#             1. A clear question text
#             2. Four options labeled A, B, C, and D
#             3. One correct answer (must be one of: A, B, C, or D)
#             4. A detailed explanation
#             5. A difficulty level (easy, medium, or hard)

#             Return ONLY a valid JSON array with this exact structure:
#             [
#                 {{
#                     "question": "What is...",
#                     "options": ["A. First option", "B. Second option", "C. Third option", "D. Fourth option"],
#                     "correct_answer": "A",
#                     "explanation": "Explanation text",
#                     "difficulty": "medium"
#                 }},
#                 ...
#             ]

#             Do not include any text before or after the JSON array.
#             Ensure the JSON is properly formatted and valid.
#             """,
#             input_variables=["topic", "subject_name", "num_questions"]
#         )

#         # Generate MCQs using the LLM
#         try:
#             mcq_generation = self.llm.predict(
#                 general_mcq_prompt.format(
#                     topic=topic,
#                     subject_name=subject_name,
#                     num_questions=num_questions
#                 )
#             )

#             # Validate JSON format
#             json.loads(mcq_generation)

#             return mcq_generation
#         except json.JSONDecodeError:
#             # If JSON is invalid, try again with a more explicit prompt
#             fallback_prompt = PromptTemplate(
#                 template="""Generate {num_questions} multiple choice questions about {topic}.

#                 Return ONLY a valid JSON array with this exact structure:
#                 [
#                     {{
#                         "question": "What is...",
#                         "options": ["A. First option", "B. Second option", "C. Third option", "D. Fourth option"],
#                         "correct_answer": "A",
#                         "explanation": "Explanation text",
#                         "difficulty": "medium"
#                     }}
#                 ]

#                 Do not include any text before or after the JSON array.
#                 """,
#                 input_variables=["topic", "num_questions"]
#             )

#             mcq_generation = self.llm.predict(
#                 fallback_prompt.format(
#                     topic=topic,
#                     num_questions=num_questions
#                 )
#             )

#             return mcq_generation
