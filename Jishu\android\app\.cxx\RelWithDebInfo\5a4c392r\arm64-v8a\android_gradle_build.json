{"buildFiles": ["D:\\Jishu\\jishu\\Jishu\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Jishu\\jishu\\Jishu\\android\\app\\.cxx\\RelWithDebInfo\\5a4c392r\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Jishu\\jishu\\Jishu\\android\\app\\.cxx\\RelWithDebInfo\\5a4c392r\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\70859756a23d9369ba2f1d3d9df6e3e4\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\70859756a23d9369ba2f1d3d9df6e3e4\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnsvg", "output": "D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\70859756a23d9369ba2f1d3d9df6e3e4\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "D:\\Jishu\\jishu\\Jishu\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5a4c392r\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\70859756a23d9369ba2f1d3d9df6e3e4\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ba9dda03132283e033c43cd4d508fdc\\transformed\\react-android-0.77.1-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}