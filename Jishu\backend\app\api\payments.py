from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.api import bp
from app.models.user import User
from app.models.exam import Subject
from app.models.payment import Payment, Purchase, PaymentPurchase
from datetime import datetime, timezone
from decimal import Decimal

@bp.route('/payments', methods=['GET'])
@jwt_required()
def get_user_payments():
    """Get all payments made by the current user"""
    current_user_id = get_jwt_identity()

    payments = Payment.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': payment.id,
        'amount': float(payment.amount),
        'currency': payment.currency,
        'payment_method': payment.payment_method,
        'transaction_id': payment.transaction_id,
        'status': payment.status,
        'payment_date': payment.payment_date.isoformat() if payment.payment_date else None,
        'purchases': [{
            'id': purchase.id,
            'subject_id': purchase.subject_id,
            'subject_name': purchase.subject.name if purchase.subject else None,
            'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
            'max_retakes': purchase.max_retakes,
            'retake_used': purchase.retake_used,
            'marks': purchase.marks,
            'negative_marks': purchase.negative_marks
        } for purchase in payment.purchases]
    } for payment in payments])

@bp.route('/payments/<int:payment_id>', methods=['GET'])
@jwt_required()
def get_payment_details(payment_id):
    """Get details of a specific payment"""
    current_user_id = get_jwt_identity()

    payment = Payment.query.get_or_404(payment_id)

    if payment.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this payment'}), 403

    return jsonify({
        'id': payment.id,
        'amount': float(payment.amount),
        'currency': payment.currency,
        'payment_method': payment.payment_method,
        'transaction_id': payment.transaction_id,
        'status': payment.status,
        'payment_date': payment.payment_date.isoformat() if payment.payment_date else None,
        'purchases': [{
            'id': purchase.id,
            'subject_id': purchase.subject_id,
            'subject_name': purchase.subject.name if purchase.subject else None,
            'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
            'max_retakes': purchase.max_retakes,
            'retake_used': purchase.retake_used,
            'marks': purchase.marks,
            'negative_marks': purchase.negative_marks
        } for purchase in payment.purchases]
    })

@bp.route('/purchases', methods=['GET'])
@jwt_required()
def get_user_purchases():
    """Get all purchases made by the current user"""
    current_user_id = get_jwt_identity()

    purchases = Purchase.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': purchase.id,
        'subject_id': purchase.subject_id,
        'subject_name': purchase.subject.name if purchase.subject else None,
        'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
        'max_retakes': purchase.max_retakes,
        'retake_used': purchase.retake_used,
        'marks': purchase.marks,
        'negative_marks': purchase.negative_marks
    } for purchase in purchases])

@bp.route('/purchases/<int:purchase_id>', methods=['GET'])
@jwt_required()
def get_purchase_details(purchase_id):
    """Get details of a specific purchase"""
    current_user_id = get_jwt_identity()

    purchase = Purchase.query.get_or_404(purchase_id)

    if purchase.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this purchase'}), 403

    return jsonify({
        'id': purchase.id,
        'subject_id': purchase.subject_id,
        'subject_name': purchase.subject.name if purchase.subject else None,
        'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
        'max_retakes': purchase.max_retakes,
        'retake_used': purchase.retake_used,
        'marks': purchase.marks,
        'negative_marks': purchase.negative_marks,
        'payments': [{
            'id': payment.id,
            'amount': float(payment.amount),
            'status': payment.status,
            'payment_date': payment.payment_date.isoformat() if payment.payment_date else None
        } for payment in purchase.payments]
    })

@bp.route('/subjects/<int:subject_id>/purchase', methods=['POST'])
@jwt_required()
def purchase_subject(subject_id):
    """Purchase a subject and create a payment record"""
    current_user_id = get_jwt_identity()

    # Check if subject exists
    subject = Subject.query.get_or_404(subject_id)

    data = request.get_json() or {}

    # Create a new purchase
    purchase = Purchase(
        user_id=current_user_id,
        subject_id=subject_id,
        purchase_date=datetime.now(timezone.utc),
        max_retakes=data.get('max_retakes', 3),
        retake_used=0,
        marks=data.get('marks', 4),
        negative_marks=data.get('negative_marks', 1)
    )

    db.session.add(purchase)
    db.session.flush()  # Get the purchase ID

    # Create a payment record
    payment_amount = data.get('amount', Decimal('499.00'))  # Default price
    payment = Payment(
        user_id=current_user_id,
        amount=payment_amount,
        currency=data.get('currency', 'INR'),
        payment_method=data.get('payment_method', 'credit_card'),
        transaction_id=data.get('transaction_id', f'TXN{datetime.now().strftime("%Y%m%d%H%M%S")}'),
        status=data.get('status', 'pending'),
        payment_date=datetime.now(timezone.utc)
    )

    db.session.add(payment)
    db.session.flush()  # Get the payment ID

    # Create payment_purchase association
    payment_purchase = PaymentPurchase(
        payment_id=payment.id,
        purchase_id=purchase.id,
        amount=payment_amount
    )

    db.session.add(payment_purchase)
    db.session.commit()

    return jsonify({
        'purchase': {
            'id': purchase.id,
            'subject_id': purchase.subject_id,
            'subject_name': subject.name,
            'purchase_date': purchase.purchase_date.isoformat(),
            'max_retakes': purchase.max_retakes,
            'retake_used': purchase.retake_used
        },
        'payment': {
            'id': payment.id,
            'amount': float(payment.amount),
            'currency': payment.currency,
            'payment_method': payment.payment_method,
            'transaction_id': payment.transaction_id,
            'status': payment.status,
            'payment_date': payment.payment_date.isoformat()
        }
    }), 201

@bp.route('/payments/<int:payment_id>/update-status', methods=['PUT'])
@jwt_required()
def update_payment_status(payment_id):
    """Update the status of a payment (e.g., after payment gateway callback)"""
    current_user_id = get_jwt_identity()

    # Check if payment exists and belongs to the current user
    payment = Payment.query.get_or_404(payment_id)

    if payment.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this payment'}), 403

    data = request.get_json() or {}

    if 'status' not in data:
        return jsonify({'error': 'Status is required'}), 400

    # Update payment status
    payment.status = data['status']

    # Update transaction ID if provided
    if 'transaction_id' in data:
        payment.transaction_id = data['transaction_id']

    # Update payment method if provided
    if 'payment_method' in data:
        payment.payment_method = data['payment_method']

    db.session.commit()

    return jsonify({
        'id': payment.id,
        'status': payment.status,
        'transaction_id': payment.transaction_id,
        'payment_method': payment.payment_method,
        'updated_at': payment.updated_at.isoformat() if payment.updated_at else None
    })
