# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 27ms
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 15ms
  [gap of 11ms]
generate_cxx_metadata completed in 36ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 30ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 20ms
generate_cxx_metadata completed in 39ms

