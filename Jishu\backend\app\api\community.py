from flask import request, jsonify
from app import db
from app.api import bp
from app.models.user import User
from app.models.community import CommunityPost, CommunityComment, CommunityLike
from datetime import datetime

@bp.route('/community/posts', methods=['GET'])
def get_posts():
    """Get all community posts with pagination"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # Get posts with pagination
    posts = CommunityPost.query.order_by(CommunityPost.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False)

    return jsonify({
        'posts': [post.to_dict() for post in posts.items],
        'total': posts.total,
        'pages': posts.pages,
        'current_page': page
    })

@bp.route('/community/posts/<int:post_id>', methods=['GET'])
def get_post(post_id):
    """Get a specific post with its comments"""
    post = CommunityPost.query.get_or_404(post_id)

    # Get comments for this post
    comments = CommunityComment.query.filter_by(post_id=post_id).order_by(CommunityComment.created_at).all()

    return jsonify({
        'post': post.to_dict(),
        'comments': [comment.to_dict() for comment in comments]
    })

@bp.route('/community/posts/<int:post_id>/comments', methods=['GET'])
def get_post_comments(post_id):
    """Get comments for a specific post"""
    post = CommunityPost.query.get_or_404(post_id)

    # Get comments for this post
    comments = CommunityComment.query.filter_by(post_id=post_id).order_by(CommunityComment.created_at.desc()).all()

    return jsonify([comment.to_dict() for comment in comments])

@bp.route('/community/posts', methods=['POST'])
def create_post():
    """Create a new community post"""
    # Get data from request
    data = request.get_json() or {}

    if 'content' not in data or not data['content'].strip():
        return jsonify({'error': 'Content is required'}), 400

    # Get user information from request headers or data
    user_id = request.headers.get('X-User-ID')
    user_name = request.headers.get('X-User-Name')

    # If headers are not provided, try to get from the request data
    if not user_id or not user_name:
        user_id = data.get('user_id')
        user_name = data.get('user_name')

    # If still no user info, get the current authenticated user if available
    if not user_id or not user_name:
        # For development, get the first user from the database
        user = User.query.first()
        if not user:
            return jsonify({'error': 'No users found in the database'}), 500
    else:
        # Check if user exists, if not create a temporary one
        user = User.query.get(user_id)
        if not user:
            user = User.query.filter_by(name=user_name).first()
            if not user:
                # Use the first user as a fallback
                user = User.query.first()
                if not user:
                    return jsonify({'error': 'No users found in the database'}), 500

    post = CommunityPost(
        user_id=user.id,
        title=data.get('title', ''),
        content=data['content'],
        image_url=data.get('image_url', '')
    )

    db.session.add(post)
    db.session.commit()

    return jsonify(post.to_dict()), 201

@bp.route('/community/posts/<int:post_id>/comments', methods=['POST'])
def add_comment(post_id):
    """Add a comment to a post"""
    post = CommunityPost.query.get_or_404(post_id)

    data = request.get_json() or {}

    if 'content' not in data or not data['content'].strip():
        return jsonify({'error': 'Content is required'}), 400

    # Get user information from request headers or data
    user_id = request.headers.get('X-User-ID')
    user_name = request.headers.get('X-User-Name')

    # If headers are not provided, try to get from the request data
    if not user_id or not user_name:
        user_id = data.get('user_id')
        user_name = data.get('user_name')

    # If still no user info, get the current authenticated user if available
    if not user_id or not user_name:
        # For development, get the first user from the database
        user = User.query.first()
        if not user:
            return jsonify({'error': 'No users found in the database'}), 500
    else:
        # Check if user exists, if not create a temporary one
        user = User.query.get(user_id)
        if not user:
            user = User.query.filter_by(name=user_name).first()
            if not user:
                # Use the first user as a fallback
                user = User.query.first()
                if not user:
                    return jsonify({'error': 'No users found in the database'}), 500

    comment = CommunityComment(
        post_id=post_id,
        user_id=user.id,
        content=data['content']
    )

    # Increment comment count on post
    post.comments_count += 1

    db.session.add(comment)
    db.session.commit()

    return jsonify(comment.to_dict()), 201

@bp.route('/community/posts/<int:post_id>/like', methods=['POST'])
def like_post(post_id):
    """Like or unlike a post"""
    # For development, we'll allow likes without authentication
    post = CommunityPost.query.get_or_404(post_id)

    # Get the first user from the database (for development)
    user = User.query.first()
    if not user:
        return jsonify({'error': 'No users found in the database'}), 500

    # Check if user already liked this post
    existing_like = CommunityLike.query.filter_by(
        user_id=user.id,
        post_id=post_id
    ).first()

    if existing_like:
        # Unlike the post
        db.session.delete(existing_like)
        post.likes_count = max(0, post.likes_count - 1)
        db.session.commit()
        return jsonify({'message': 'Post unliked successfully', 'likes_count': post.likes_count})
    else:
        # Like the post
        like = CommunityLike(
            user_id=user.id,
            post_id=post_id
        )
        post.likes_count += 1
        db.session.add(like)
        db.session.commit()
        return jsonify({'message': 'Post liked successfully', 'likes_count': post.likes_count})

@bp.route('/community/comments/<int:comment_id>/like', methods=['POST'])
def like_comment(comment_id):
    """Like or unlike a comment"""
    # For development, we'll allow likes without authentication
    comment = CommunityComment.query.get_or_404(comment_id)

    # Get the first user from the database (for development)
    user = User.query.first()
    if not user:
        return jsonify({'error': 'No users found in the database'}), 500

    # Check if user already liked this comment
    existing_like = CommunityLike.query.filter_by(
        user_id=user.id,
        comment_id=comment_id
    ).first()

    if existing_like:
        # Unlike the comment
        db.session.delete(existing_like)
        comment.likes_count = max(0, comment.likes_count - 1)
        db.session.commit()
        return jsonify({'message': 'Comment unliked successfully', 'likes_count': comment.likes_count})
    else:
        # Like the comment
        like = CommunityLike(
            user_id=user.id,
            comment_id=comment_id
        )
        comment.likes_count += 1
        db.session.add(like)
        db.session.commit()
        return jsonify({'message': 'Comment liked successfully', 'likes_count': comment.likes_count})
