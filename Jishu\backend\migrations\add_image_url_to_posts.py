"""
Migration to add image_url column to community_posts table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app import create_app, db

def upgrade():
    """Add image_url column to community_posts table"""
    app = create_app()
    with app.app_context():
        # Add image_url column
        with db.engine.connect() as conn:
            from sqlalchemy import text
            conn.execute(text('''
            ALTER TABLE community_posts
            ADD COLUMN image_url VARCHAR(255) DEFAULT NULL
            '''))
            conn.commit()
            print("Added image_url column to community_posts table")

if __name__ == "__main__":
    upgrade()
