import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './src/store';
import { useSelector, useDispatch } from 'react-redux';
import { logoutUser } from './src/store/slices/authSlice';
import { ThemeProvider } from './src/context/ThemeContext';
import AppNavigationContainer from './src/components/AppNavigationContainer';

// Import profile completion handler
import ProfileCompletionHandler from './src/components/auth/ProfileCompletionHandler';

// Import screens
import LoginScreen from './src/screens/LoginScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import FullExamListScreen from './src/screens/FullExamListScreen';
import CommunityScreen from './src/screens/CommunityScreen';
import ChatbotScreen from './src/screens/ChatbotScreen';
import Header from './src/screens/Header';
import QuestionTypeSelectionScreen from './src/screens/QuestionTypeSelectionScreen';
import FullMockTestScreen from './src/screens/FullMockTestScreen';
import TestInstructions from './src/screens/TestInstructions';
import AchievementsScreen from './src/screens/AchievementsScreen';
import ExamQuestionsScreen from './src/screens/ExamQuestionsScreen';
import ConnectionTestScreen from './src/screens/ConnectionTestScreen';

// Import admin screens
import AdminDashboard from './src/screens/admin/AdminDashboard';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();
const AdminStack = createStackNavigator();

// Admin Navigator
const AdminNavigator = () => (
  <AdminStack.Navigator>
    <AdminStack.Screen
      name="AdminDashboard"
      component={AdminDashboard}
      options={{ title: 'Admin Dashboard' }}
    />
    {/* Add more admin screens here */}
  </AdminStack.Navigator>
);

// Bottom Tab Navigator
const MainTabs = () => {
  return (
    <AppNavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ color, size }) => {
            let iconName = '';
            if (route.name === 'Dashboard') {
              iconName = 'dashboard';
            } else if (route.name === 'Exams') {
              iconName = 'list-alt';
            } else if (route.name === 'Community') {
              iconName = 'chat';
            } else if (route.name === 'Chatbot') {
              iconName = 'smart-toy';
            }
            return <Icon name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#3461eb',
          tabBarInactiveTintColor: '#666',
        })}
      >
    <Tab.Screen
      name="Dashboard"
      component={DashboardScreen}
      options={{
        headerShown: false,
      }}
    />
    <Tab.Screen
      name="Exams"
      component={FullExamListScreen}
      options={{
        headerShown: false,
      }}
    />
    <Tab.Screen
      name="Community"
      component={CommunityScreen}
      options={{
        headerShown: false,
      }}
    />
    <Tab.Screen
      name="Chatbot"
      component={ChatbotScreen}
      options={{
        headerShown: false,
      }}
    />
      </Tab.Navigator>
    </AppNavigationContainer>
  );
};

// App Container with Redux
const AppContainer = () => {
  const { isAuthenticated, user } = useSelector((state) => state.auth);

  // Determine which navigator to show based on user role
  const getInitialRoute = () => {
    if (!isAuthenticated) {
      return 'Login';
    }

    if (user?.role === 'admin') {
      return 'AdminNavigator';
    }

    // Student role or any other role defaults to MainTabs
    return 'MainTabs';
  };

  return (
    <NavigationContainer>
      {/* Profile Completion Handler */}
      <ProfileCompletionHandler />

      <Stack.Navigator initialRouteName={getInitialRoute()}>
        {!isAuthenticated ? (
          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{ headerShown: false }}
          />
        ) : (
          <>
            {user?.role === 'admin' ? (
              // Admin role - show admin dashboard
              <Stack.Screen
                name="AdminNavigator"
                component={AdminNavigator}
                options={{ headerShown: false }}
              />
            ) : (
              // Student role - show main app
              <Stack.Screen
                name="MainTabs"
                component={MainTabs}
                options={{ headerShown: false }}
              />
            )}
          </>
        )}

        {/* Add other screens here */}

        <Stack.Screen
          name="FullExamList"
          component={FullExamListScreen}
          options={{ title: 'All Exams' }}
        />
        <Stack.Screen
          name="QuestionTypeSelectionScreen"
          component={QuestionTypeSelectionScreen}
          options={{ title: 'Select Question Type' }}
        />

        <Stack.Screen
          name="FullMockTestScreen"
          component={FullMockTestScreen}
          options={{ title: 'Full Mock Test' }}
        />
        <Stack.Screen
          name="TestInstructions"
          component={TestInstructions}
          options={{ title: 'Test Instructions' }}
        />
        <Stack.Screen
          name="Achivements"
          component={AchievementsScreen}
          options={{ title: 'Your Achievements' }}
        />
        <Stack.Screen
          name="ExamQuestionsScreen"
          component={ExamQuestionsScreen}
          options={{ headerShown: false }}
        />

        {/* Connection Test Screen */}
        <Stack.Screen
          name="ConnectionTest"
          component={ConnectionTestScreen}
          options={{ title: 'API Connection Test' }}
        />

        {/* Chat Screen */}
        <Stack.Screen
          name="ChatbotScreen"
          component={ChatbotScreen}
          options={{
            headerShown: false,
            presentation: 'modal'
          }}
        />

      </Stack.Navigator>
    </NavigationContainer>
  );
};

// Root component
const App = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider>
          <AppContainer />
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
};

export default App;
