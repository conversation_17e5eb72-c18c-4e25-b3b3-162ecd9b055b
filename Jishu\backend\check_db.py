from app import create_app, db
from app.models.exam import ExamCategory, Subject

app = create_app()

with app.app_context():
    print('Exam Categories:')
    categories = ExamCategory.query.all()
    for c in categories:
        print(f'- {c.id}: {c.name} ({c.description})')
    
    print('\nSubjects:')
    subjects = Subject.query.all()
    for s in subjects:
        print(f'- {s.id}: {s.subject_name} (Category: {s.category.name if s.category else None})')
