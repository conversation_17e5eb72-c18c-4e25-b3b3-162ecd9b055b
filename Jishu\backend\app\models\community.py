from app import db
from datetime import datetime

class CommunityPost(db.Model):
    __tablename__ = 'community_posts'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    title = db.Column(db.String(255))
    content = db.Column(db.Text, nullable=False)
    image_url = db.Column(db.String(255))
    likes_count = db.Column(db.Integer, default=0)
    comments_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('posts', lazy='dynamic'))
    comments = db.relationship('CommunityComment', backref='post', lazy='dynamic', cascade='all, delete-orphan')
    likes = db.relationship('CommunityLike', backref='post', lazy='dynamic',
                           primaryjoin="and_(CommunityLike.post_id==CommunityPost.id, CommunityLike.comment_id==None)",
                           cascade='all, delete-orphan')

    __table_args__ = (
        db.Index('idx_user_id', 'user_id'),
        db.Index('idx_created_at', 'created_at'),
    )

    def __repr__(self):
        return f'<CommunityPost {self.id} User:{self.user_id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.name if self.user else None,
            'user_avatar': self.user.icon if self.user else None,  # Use icon instead of avatar
            'title': self.title,
            'content': self.content,
            'image_url': self.image_url,
            'likes_count': self.likes_count,
            'comments_count': self.comments_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class CommunityComment(db.Model):
    __tablename__ = 'community_comments'

    id = db.Column(db.Integer, primary_key=True)
    post_id = db.Column(db.Integer, db.ForeignKey('community_posts.id', ondelete='CASCADE'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    likes_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('comments', lazy='dynamic'))
    likes = db.relationship('CommunityLike', backref='comment', lazy='dynamic',
                           primaryjoin="and_(CommunityLike.comment_id==CommunityComment.id, CommunityLike.post_id==None)",
                           cascade='all, delete-orphan')

    __table_args__ = (
        db.Index('idx_post_id', 'post_id'),
        db.Index('idx_user_id', 'user_id'),
    )

    def __repr__(self):
        return f'<CommunityComment {self.id} Post:{self.post_id} User:{self.user_id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'post_id': self.post_id,
            'user_id': self.user_id,
            'user_name': self.user.name if self.user else None,
            'user_avatar': self.user.avatar if self.user else None,
            'content': self.content,
            'likes_count': self.likes_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class CommunityLike(db.Model):
    __tablename__ = 'community_likes'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    post_id = db.Column(db.Integer, db.ForeignKey('community_posts.id', ondelete='CASCADE'))
    comment_id = db.Column(db.Integer, db.ForeignKey('community_comments.id', ondelete='CASCADE'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('likes', lazy='dynamic'))

    __table_args__ = (
        db.UniqueConstraint('user_id', 'post_id', name='unique_user_post'),
        db.UniqueConstraint('user_id', 'comment_id', name='unique_user_comment'),
        db.CheckConstraint('(post_id IS NOT NULL OR comment_id IS NOT NULL)', name='check_target_exists'),
        db.CheckConstraint('NOT (post_id IS NOT NULL AND comment_id IS NOT NULL)', name='check_single_target'),
    )

    def __repr__(self):
        target = f"Post:{self.post_id}" if self.post_id else f"Comment:{self.comment_id}"
        return f'<CommunityLike {self.id} User:{self.user_id} {target}>'
