"""
<PERSON><PERSON><PERSON> to add dummy community posts and comments to the Jishu application.
"""

import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.user import User
from app.models.community import CommunityPost, CommunityComment, CommunityLike
from datetime import datetime, timedelta
import random

def add_community_data():
    """Add dummy community posts, comments, and likes"""
    app = create_app()
    with app.app_context():
        print("Adding community data to the database...")

        # Check if data already exists
        if db.session.query(CommunityPost).count() > 0:
            print("Community posts already exist in the database. Skipping creation.")
            return

        # Get users
        users = db.session.query(User).all()
        if not users:
            print("No users found in the database. Please run add_initial_dummy_data.py first.")
            return

        # Add community posts
        posts = add_community_posts(users)

        # Add comments to posts
        comments = add_community_comments(users, posts)

        # Add likes to posts and comments
        likes = add_community_likes(users, posts, comments)

        print("Community data added successfully!")

def add_community_posts(users):
    """Add dummy community posts"""
    print("Adding community posts...")

    posts = []

    # Sample post content
    post_contents = [
        "Just finished my NEET practice test! Scored 85%. The biology section was challenging but I managed to get through it. Any tips for improving in biology?",
        "Anyone else struggling with JEE Advanced mechanics problems? I'm finding it hard to visualize the force diagrams.",
        "Just got my CET results! So happy with my performance. The practice tests on this app really helped me prepare well.",
        "Looking for study partners for GATE CSE preparation. Anyone interested?",
        "How do you guys manage time during the JEE exam? I always run out of time in the math section.",
        "What's the best way to memorize organic chemistry reactions for NEET?",
        "Just purchased the Physics test package. The questions are really challenging!",
        "Any recommendations for good reference books for JEE Chemistry?",
        "Feeling overwhelmed with the NEET syllabus. How are you all managing to cover everything?",
        "Just scored 92% on my Biology mock test! The hard work is finally paying off.",
        "What's your strategy for solving numerical problems in Physics?",
        "Is anyone else preparing for both NEET and AIIMS?",
        "The explanation feature in the practice tests is so helpful! It's helping me understand my mistakes.",
        "How many hours are you all studying daily for JEE preparation?",
        "Just joined this community. Looking forward to connecting with fellow aspirants!"
    ]

    # Skip admin user (first user)
    student_users = [user for user in users if user.role == 'student']

    # Create posts
    for i, content in enumerate(post_contents):
        # Select a random user
        user = random.choice(student_users)

        # Create post with random timestamp within the last 30 days
        days_ago = random.randint(0, 30)
        post = CommunityPost(
            user_id=user.id,
            content=content,
            created_at=datetime.utcnow() - timedelta(days=days_ago),
            updated_at=datetime.utcnow() - timedelta(days=days_ago)
        )
        posts.append(post)

    db.session.add_all(posts)
    db.session.commit()

    print(f"Added {len(posts)} community posts")
    return posts

def add_community_comments(users, posts):
    """Add dummy comments to community posts"""
    print("Adding community comments...")

    comments = []

    # Sample comment content
    comment_contents = [
        "Great job! Keep it up!",
        "I had the same issue. Try focusing on diagrams and flowcharts. It helped me a lot.",
        "NCERT is the key for biology. Make sure you've covered it thoroughly.",
        "Try HC Verma's book. The examples are really helpful for visualization.",
        "Congratulations! What was your score?",
        "I'm interested! Let's form a study group.",
        "I usually allocate specific time for each section and stick to it.",
        "Practice is the key. Try solving at least 50 problems daily.",
        "I found YouTube tutorials really helpful for this topic.",
        "Have you tried the mock tests on this app? They're quite good!",
        "I'm struggling with the same thing. Let me know if you find a solution.",
        "That's impressive! What's your study routine like?",
        "I recommend solving previous years' papers. They give a good idea of the exam pattern.",
        "Don't worry, you'll get there. Just stay consistent with your studies.",
        "I'm in the same boat. Let's connect and study together!"
    ]

    # Skip admin user
    student_users = [user for user in users if user.role == 'student']

    # Add 1-5 comments to each post
    for post in posts:
        num_comments = random.randint(1, 5)

        for _ in range(num_comments):
            # Select a random user (different from post author)
            available_users = [user for user in student_users if user.id != post.user_id]
            if not available_users:
                continue

            user = random.choice(available_users)

            # Select a random comment content
            content = random.choice(comment_contents)

            # Create comment with timestamp after post creation but before now
            post_time = post.created_at
            now = datetime.utcnow()
            max_seconds = int((now - post_time).total_seconds())
            if max_seconds <= 0:
                comment_time = now
            else:
                random_seconds = random.randint(60, min(max_seconds, 86400 * 7))  # Between 1 minute and 7 days after post
                comment_time = post_time + timedelta(seconds=random_seconds)

            comment = CommunityComment(
                post_id=post.id,
                user_id=user.id,
                content=content,
                created_at=comment_time,
                updated_at=comment_time
            )
            comments.append(comment)

            # Increment comment count on post
            post.comments_count += 1

    db.session.add_all(comments)
    db.session.commit()

    print(f"Added {len(comments)} community comments")
    return comments

def add_community_likes(users, posts, comments):
    """Add likes to posts and comments"""
    print("Adding community likes...")

    likes = []

    # Skip admin user
    student_users = [user for user in users if user.role == 'student']

    # Add likes to posts (30-70% of users like each post)
    for post in posts:
        # Users who can like (exclude post author)
        available_users = [user for user in student_users if user.id != post.user_id]

        # Determine number of likes
        num_likes = random.randint(int(len(available_users) * 0.3), int(len(available_users) * 0.7))
        num_likes = min(num_likes, len(available_users))

        # Select random users to like the post
        liking_users = random.sample(available_users, num_likes)

        for user in liking_users:
            like = CommunityLike(
                user_id=user.id,
                post_id=post.id,
                created_at=datetime.utcnow() - timedelta(minutes=random.randint(5, 1440))
            )
            likes.append(like)

            # Increment likes count on post
            post.likes_count += 1

    # Add likes to comments (20-50% of users like each comment)
    for comment in comments:
        # Users who can like (exclude comment author and post author)
        post = next(post for post in posts if post.id == comment.post_id)
        available_users = [user for user in student_users if user.id != comment.user_id and user.id != post.user_id]

        if not available_users:
            continue

        # Determine number of likes
        num_likes = random.randint(int(len(available_users) * 0.2), int(len(available_users) * 0.5))
        num_likes = min(num_likes, len(available_users))

        # Select random users to like the comment
        liking_users = random.sample(available_users, num_likes)

        for user in liking_users:
            like = CommunityLike(
                user_id=user.id,
                comment_id=comment.id,
                created_at=datetime.utcnow() - timedelta(minutes=random.randint(5, 1440))
            )
            likes.append(like)

            # Increment likes count on comment
            comment.likes_count += 1

    db.session.add_all(likes)
    db.session.commit()

    print(f"Added {len(likes)} community likes")
    return likes

if __name__ == "__main__":
    add_community_data()
