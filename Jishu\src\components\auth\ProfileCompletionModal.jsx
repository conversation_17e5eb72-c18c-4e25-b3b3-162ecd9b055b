import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { completeProfile } from '../../store/slices/authSlice';
import { authAPI } from '../../services/api';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  error: '#F44336',
};

// Local avatar options - using only 4 unique avatars to avoid duplicates
const AVATARS = [
  { id: 1, source: require('../../../assets/images/engg1.jpg'), filename: 'engg1.jpg' },
  { id: 2, source: require('../../../assets/images/engg2.jpg'), filename: 'engg2.jpg' },
  { id: 3, source: require('../../../assets/images/doc.jpg'), filename: 'doc.jpg' },
  { id: 4, source: require('../../../assets/images/index.png'), filename: 'index.png' },
];

const ProfileCompletionModal = ({ visible, onClose, onCompleteProfile, user }) => {
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state) => state.auth);

  const [name, setName] = useState('');
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!selectedAvatar) {
      newErrors.avatar = 'Please select an avatar';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Get the avatar filename from the selected avatar
      if (!selectedAvatar) {
        throw new Error('Please select an avatar');
      }

      // Use the filename property directly
      const avatarPath = selectedAvatar.filename;

      console.log('Selected avatar:', selectedAvatar);
      console.log('Avatar filename:', avatarPath);

      // Prepare profile data
      const profileData = {
        name: name.trim(),
        avatar: avatarPath,
        mobile_number: user?.mobile_number, // Include the mobile number
      };

      console.log('Profile data to be sent:', profileData);

      // Make sure we have the mobile number
      if (!user?.mobile_number) {
        throw new Error('Mobile number not found. Please try logging in again.');
      }

      console.log('Sending profile data:', profileData);

      // Use the onCompleteProfile prop to handle the profile completion
      const success = await onCompleteProfile(profileData);

      if (success) {
        console.log('Profile completed successfully');
        // The modal will be closed automatically when isProfileComplete becomes true
      } else {
        throw new Error('Failed to complete profile');
      }
    } catch (error) {
      console.error('Profile completion error:', error);
      Alert.alert(
        'Profile Update Failed',
        error.message || 'Failed to update profile. Please try again.'
      );
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Complete Your Profile</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Icon name="close" size={24} color={COLORS.textSecondary} />
            </TouchableOpacity>
          </View>
          <Text style={styles.modalSubtitle}>
            Please provide your name and select an avatar to continue
          </Text>

          <ScrollView style={styles.formContainer}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              placeholder="Enter your name"
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (errors.name) {
                  setErrors({ ...errors, name: null });
                }
              }}
            />
            {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}

            <Text style={styles.label}>Select an Avatar</Text>
            {errors.avatar && <Text style={styles.errorText}>{errors.avatar}</Text>}

            <View style={styles.avatarGrid}>
              {AVATARS.map((avatar) => (
                <TouchableOpacity
                  key={avatar.id}
                  style={[
                    styles.avatarItem,
                    selectedAvatar?.id === avatar.id && styles.selectedAvatarItem,
                  ]}
                  onPress={() => {
                    setSelectedAvatar(avatar);
                    if (errors.avatar) {
                      setErrors({ ...errors, avatar: null });
                    }
                  }}
                >
                  <Image source={avatar.source} style={styles.avatarImage} />
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#FFFFFF" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>Complete Profile</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
  },
  closeButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: COLORS.text,
    flex: 1,
  },
  modalSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
  },
  formContainer: {
    maxHeight: '70%',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  inputError: {
    borderColor: COLORS.error,
  },
  errorText: {
    color: COLORS.error,
    fontSize: 14,
    marginBottom: 8,
  },
  avatarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  avatarItem: {
    width: '23%',
    aspectRatio: 1,
    marginBottom: 10,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    overflow: 'hidden',
  },
  selectedAvatarItem: {
    borderColor: COLORS.primary,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  buttonContainer: {
    marginTop: 10,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileCompletionModal;
