import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI } from '../../services/api';

// For debugging
console.log('Auth slice initialized');

// Async thunk for requesting OTP
export const requestOTP = createAsyncThunk(
  'auth/requestOTP',
  async ({ mobile }, { rejectWithValue }) => {
    try {
      console.log('Requesting OTP for mobile:', mobile);

      // Call the backend API to request OTP
      const response = await authAPI.requestOTP(mobile);
      console.log('OTP request response:', response);

      // In a real app, the OTP would be sent to the user's mobile
      // For development, the backend will use '123456' as the OTP

      return { mobile, success: true, message: 'OTP sent successfully' };
    } catch (error) {
      console.error('Error requesting OTP:', error);

      // Check if it's a network error
      if (error.message && error.message.includes('Network request failed')) {
        return rejectWithValue('Network error: Please check your connection or ensure the backend server is running. Using 123456 as OTP for development.');
      }

      // For development, we'll still allow login with the hardcoded OTP
      if (error.message && error.message.includes('Authentication server is not available')) {
        return {
          mobile,
          success: true,
          message: 'Backend server is not running. Using 123456 as OTP for development.'
        };
      }

      return rejectWithValue(error.message || 'Failed to send OTP');
    }
  }
);

// Async thunk for verifying OTP and logging in
export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ mobile, otp }, { rejectWithValue }) => {
    try {
      console.log('Verifying OTP for mobile:', mobile, 'OTP:', otp);

      // For development, if OTP is 123456, we'll allow login even if the backend is down
      if (otp === '123456') {
        try {
          // Try to call the backend API to verify OTP
          const response = await authAPI.verifyOTP(mobile, otp);
          console.log('OTP verification response:', response);

          // Store tokens in AsyncStorage for persistence
          if (response.access_token) {
            await AsyncStorage.setItem('accessToken', response.access_token);
          }
          if (response.refresh_token) {
            await AsyncStorage.setItem('refreshToken', response.refresh_token);
          }

          // Return the complete response including user data and tokens
          return {
            user: response.user,
            is_new_user: response.is_new_user,
            access_token: response.access_token,
            refresh_token: response.refresh_token
          };
        } catch (apiError) {
          console.error('API error during OTP verification:', apiError);

          // If backend is down but OTP is correct, create a temporary user for development
          if (apiError.message && (
              apiError.message.includes('Network request failed') ||
              apiError.message.includes('Authentication server is not available')
            )) {
            console.log('Backend is down, but using hardcoded OTP for development');

            // Create a temporary user and token
            const tempUser = {
              id: 1,
              mobile_number: mobile,
              name: null,
              avatar: null,
              is_profile_complete: false,
              created_at: new Date().toISOString()
            };

            const tempToken = `dev_token_${Date.now()}`;
            await AsyncStorage.setItem('accessToken', tempToken);

            return {
              user: tempUser,
              is_new_user: true,
              access_token: tempToken,
              refresh_token: null
            };
          }

          // If it's another type of error, rethrow it
          throw apiError;
        }
      } else {
        // If OTP is not 123456, verify normally
        const response = await authAPI.verifyOTP(mobile, otp);
        console.log('OTP verification response:', response);

        // Store tokens in AsyncStorage for persistence
        if (response.access_token) {
          await AsyncStorage.setItem('accessToken', response.access_token);
        }
        if (response.refresh_token) {
          await AsyncStorage.setItem('refreshToken', response.refresh_token);
        }

        // Return the complete response including user data and tokens
        return {
          user: response.user,
          is_new_user: response.is_new_user,
          access_token: response.access_token,
          refresh_token: response.refresh_token
        };
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);

      // Check if it's a network error
      if (error.message && error.message.includes('Network request failed')) {
        return rejectWithValue('Network error: Please check your connection or ensure the backend server is running.');
      }

      // For invalid OTP
      if (error.message && error.message.includes('Invalid OTP')) {
        return rejectWithValue('Invalid OTP. For development, use 123456.');
      }

      return rejectWithValue(error.message || 'Verification failed');
    }
  }
);

// Async thunk for logout
export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // Call the logout API
      await authAPI.logout();
      return null;
    } catch (error) {
      return rejectWithValue(error.message || 'Logout failed');
    }
  }
);

// Async thunk for updating user profile
export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      console.log('Updating user profile with data:', profileData);

      // Call the API to update the user profile
      const updatedUser = await authAPI.updateProfile(profileData);
      console.log('Profile update response:', updatedUser);

      return updatedUser;
    } catch (error) {
      console.error('Error updating profile:', error);
      return rejectWithValue(error.message || 'Failed to update profile');
    }
  }
);

// Async thunk for completing user profile after first login
export const completeProfile = createAsyncThunk(
  'auth/completeProfile',
  async (profileData, { rejectWithValue, getState }) => {
    try {
      // Get the current user's mobile number if not provided
      const state = getState();
      if (!profileData.mobile_number && state.auth.user?.mobile_number) {
        profileData.mobile_number = state.auth.user.mobile_number;
      }

      console.log('Completing user profile with data:', profileData);

      // Make sure we have a mobile number
      if (!profileData.mobile_number) {
        throw new Error('Mobile number is required');
      }

      // Make sure we have a name
      if (!profileData.name) {
        throw new Error('Name is required');
      }

      // Make sure we have an avatar
      if (!profileData.avatar) {
        throw new Error('Avatar is required');
      }

      try {
        // Call the API to complete the user profile
        console.log('Calling API to complete profile with data:', JSON.stringify(profileData));
        const response = await authAPI.completeProfile(profileData);
        console.log('Profile completion response:', JSON.stringify(response));

        if (!response || !response.user) {
          throw new Error('Invalid response from server: missing user data');
        }

        // Store tokens if they are returned
        if (response.access_token) {
          await AsyncStorage.setItem('accessToken', response.access_token);
        }
        if (response.refresh_token) {
          await AsyncStorage.setItem('refreshToken', response.refresh_token);
        }

        return response.user;
      } catch (apiError) {
        console.error('API error during profile completion:', apiError);

        // If backend is down, create a temporary updated user for development
        if (apiError.message && (
            apiError.message.includes('Network request failed') ||
            apiError.message.includes('Authentication server is not available') ||
            apiError.message.includes('Profile completion failed')
          )) {
          console.log('Backend is down, but updating user profile locally for development');

          // Get the current user and update it
          const currentUser = state.auth.user;
          const updatedUser = {
            ...currentUser,
            name: profileData.name,
            avatar: profileData.avatar,
            is_profile_complete: true,
            updated_at: new Date().toISOString()
          };

          console.log('Created local user profile:', updatedUser);
          return updatedUser;
        }

        // If it's another type of error, rethrow it
        throw apiError;
      }
    } catch (error) {
      console.error('Error completing profile:', error);

      // Check if it's a network error
      if (error.message && error.message.includes('Network request failed')) {
        return rejectWithValue('Network error: Please check your connection or ensure the backend server is running.');
      }

      return rejectWithValue(error.message || 'Failed to complete profile');
    }
  }
);

// Async thunk to check if user is already logged in
export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      // Get access token from AsyncStorage
      const token = await AsyncStorage.getItem('accessToken');

      if (!token) {
        return null;
      }

      try {
        // Call the API to get current user info
        const user = await authAPI.getCurrentUser();
        return user;
      } catch (apiError) {
        console.error('API error during auth status check:', apiError);

        // If backend is down but we have a token, create a temporary user for development
        if (apiError.message && (
            apiError.message.includes('Network request failed') ||
            apiError.message.includes('Failed to get user profile')
          )) {
          console.log('Backend is down, but token exists. Creating temporary user for development');

          // Create a basic user object for development
          return {
            id: 1,
            mobile_number: 'unknown',
            name: 'Development User',
            avatar: null,
            is_profile_complete: true,
            created_at: new Date().toISOString()
          };
        }

        // For other errors (like expired token), clear the tokens
        await AsyncStorage.removeItem('accessToken');
        await AsyncStorage.removeItem('refreshToken');
        return null;
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // If there's an error, clear the tokens
      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
      return null;
    }
  }
);

const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  otpSent: false,
  mobile: null,
  isNewUser: false,
  isProfileComplete: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetOtpState: (state) => {
      state.otpSent = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Request OTP cases
      .addCase(requestOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.otpSent = false;
      })
      .addCase(requestOTP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.otpSent = true;
        state.mobile = action.payload.mobile;
        state.error = null;
      })
      .addCase(requestOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.otpSent = false;
      })

      // Verify OTP cases
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.access_token;
        state.isNewUser = action.payload.is_new_user;
        state.isProfileComplete = action.payload.user.is_profile_complete;
        state.otpSent = false;
        state.error = null;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.isAuthenticated = false;
      })

      // Update profile cases
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isProfileComplete = action.payload.is_profile_complete;
        state.error = null;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Complete profile cases
      .addCase(completeProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(completeProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isProfileComplete = true;
        state.error = null;
      })
      .addCase(completeProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Logout cases
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.otpSent = false;
        state.mobile = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Check auth status cases
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = !!action.payload;
        state.user = action.payload;
        if (action.payload) {
          state.isProfileComplete = action.payload.is_profile_complete;
        }
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, resetOtpState } = authSlice.actions;

export default authSlice.reducer;
