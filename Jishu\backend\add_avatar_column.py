"""
Migration script to add the avatar column to the users table.
This script fixes the discrepancy between the User model and the database schema.
"""

from app import create_app, db

def upgrade():
    """Add avatar column to the users table"""
    app = create_app()
    with app.app_context():
        # Check if the column already exists
        try:
            # Try to select from the avatar column to check if it exists
            db.engine.execute('SELECT avatar FROM users LIMIT 1')
            print("Avatar column already exists in users table.")
        except Exception as e:
            if 'Unknown column' in str(e):
                print("Adding avatar column to users table...")
                # Add the avatar column
                db.engine.execute('ALTER TABLE users ADD COLUMN avatar VARCHAR(255)')
                
                # Copy data from icon column if it exists
                try:
                    db.engine.execute('UPDATE users SET avatar = icon')
                    print("Copied data from icon column to avatar column.")
                except Exception as e:
                    print(f"Could not copy data from icon column: {e}")
                
                print("Avatar column added successfully!")
            else:
                print(f"Error checking avatar column: {e}")

if __name__ == "__main__":
    upgrade()
