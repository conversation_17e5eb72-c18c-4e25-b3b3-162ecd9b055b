# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
  [gap of 12ms]
generate_cxx_metadata completed in 27ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 20ms]
generate_cxx_metadata completed in 39ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 22ms
  [gap of 16ms]
generate_cxx_metadata completed in 52ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 17ms
generate_cxx_metadata completed in 30ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 37ms]
  create-invalidation-state 36ms
  [gap of 39ms]
generate_cxx_metadata completed in 112ms

