# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
generate_cxx_metadata completed in 25ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 25ms
generate_cxx_metadata completed in 41ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 20ms
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata 25ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 22ms
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 24ms
generate_cxx_metadata completed in 42ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 27ms
generate_cxx_metadata completed in 50ms

