from app import db
from datetime import datetime, timezone

class ExamCategory(db.Model):
    __tablename__ = 'exam_categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Virtual property for icon (not in database)
    @property
    def icon(self):
        # Return a default icon based on the category name
        if 'neet' in self.name.lower():
            return 'stethoscope'
        elif 'jee' in self.name.lower():
            return 'calculator-variant-outline'
        elif 'gate' in self.name.lower():
            return 'laptop'
        else:
            return 'school'

    # Relationships
    subjects = db.relationship('Subject', back_populates='category', lazy='dynamic')

    def __repr__(self):
        return f'<ExamCategory {self.name}>'

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'icon': self.icon,  # This now uses the virtual property
            'subjects': [subject.to_dict() for subject in self.subjects]
        }

class Subject(db.Model):
    __tablename__ = 'subjects'

    id = db.Column(db.Integer, primary_key=True)
    subject_name = db.Column(db.String(100), nullable=False)  # Column name in DB is 'subject_name'
    description = db.Column(db.String(255))
    max_retakes = db.Column(db.Integer, default=3)
    exam_category_id = db.Column(db.Integer, db.ForeignKey('exam_categories.id'), nullable=False)  # Column name in DB is 'exam_category_id'
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    category = db.relationship('ExamCategory', back_populates='subjects', foreign_keys=[exam_category_id])
    questions = db.relationship('QuestionWithOptions', back_populates='subject', lazy='dynamic')

    def __repr__(self):
        return f'<Subject {self.subject_name} ({self.category.name if self.category else "Unknown"})>'

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.subject_name,  # Return as 'name' for frontend compatibility
            'description': self.description,
            'max_retakes': self.max_retakes,
            'category_id': self.exam_category_id,  # Return as 'category_id' for frontend compatibility
            'category_name': self.category.name if self.category else None,
            'question_count': self.questions.count()
        }

class QuestionWithOptions(db.Model):
    __tablename__ = 'questions_with_options'

    id = db.Column(db.Integer, primary_key=True)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=False)
    text = db.Column(db.Text, nullable=False)
    difficulty = db.Column(db.String(20), default='medium')
    option_a = db.Column(db.Text, nullable=False)
    option_b = db.Column(db.Text, nullable=False)
    option_c = db.Column(db.Text, nullable=False)
    option_d = db.Column(db.Text, nullable=False)
    correct_option = db.Column(db.String(1), nullable=False)
    explanation = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Virtual properties for compatibility
    @property
    def marks(self):
        return 4  # Default marks

    @property
    def negative_marks(self):
        return 1  # Default negative marks

    @property
    def is_ai_generated(self):
        return False  # Default value

    # Relationships
    subject = db.relationship('Subject', back_populates='questions')

    def __repr__(self):
        return f'<QuestionWithOptions {self.id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'text': self.text,
            'subject_id': self.subject_id,
            'difficulty': self.difficulty,
            'marks': self.marks,
            'negative_marks': self.negative_marks,
            'options': {
                'A': self.option_a,
                'B': self.option_b,
                'C': self.option_c,
                'D': self.option_d
            },
            'correct_option': self.correct_option,
            'explanation': self.explanation,
            'is_ai_generated': self.is_ai_generated,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# Keep the old models for reference during migration, but they will be deprecated
class Question(db.Model):
    __tablename__ = 'questions'

    id = db.Column(db.Integer, primary_key=True)
    text = db.Column(db.Text, nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=False)
    difficulty = db.Column(db.String(20), default='medium')  # easy, medium, hard
    marks = db.Column(db.Integer, default=4)  # Default marks for correct answer
    negative_marks = db.Column(db.Integer, default=1)  # Default negative marks for wrong answer
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    subject = db.relationship('Subject')
    options = db.relationship('Option', back_populates='question', lazy='dynamic')

    def __repr__(self):
        return f'<Question {self.id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'text': self.text,
            'subject_id': self.subject_id,
            'difficulty': self.difficulty,
            'marks': self.marks,
            'negative_marks': self.negative_marks,
            'options': [option.to_dict() for option in self.options]
        }

class Option(db.Model):
    __tablename__ = 'options'

    id = db.Column(db.Integer, primary_key=True)
    text = db.Column(db.Text, nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    is_correct = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    question = db.relationship('Question', back_populates='options')

    def __repr__(self):
        return f'<Option {self.id} for Question {self.question_id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'text': self.text,
            'is_correct': self.is_correct
        }

# Example query to get AI-generated questions (commented out to avoid app context issues)
# This should be used inside a function with app context, not at module level
# Example:
# def get_ai_questions(subject_id):
#     return QuestionWithOptions.query.filter_by(
#         is_ai_generated=True,
#         subject_id=subject_id
#     ).all()


