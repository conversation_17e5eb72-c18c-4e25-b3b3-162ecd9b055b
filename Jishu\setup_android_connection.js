const { execSync } = require('child_process');
const readline = require('readline');
const os = require('os');
const fs = require('fs');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to get the local IP address
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  let ipAddress = '';
  
  // Find the first non-internal IPv4 address
  Object.keys(interfaces).forEach((ifname) => {
    interfaces[ifname].forEach((iface) => {
      if (iface.family === 'IPv4' && !iface.internal) {
        ipAddress = iface.address;
      }
    });
  });
  
  return ipAddress;
}

// Function to update the API URL in the config file
function updateApiUrl(url) {
  try {
    const configPath = './src/config/index.js';
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    // Replace the API_URL line
    configContent = configContent.replace(
      /export const API_URL = '.*';/,
      `export const API_URL = '${url}';  // Updated by setup script`
    );
    
    fs.writeFileSync(configPath, configContent);
    console.log(`\nAPI URL updated to: ${url}`);
  } catch (error) {
    console.error('Error updating API URL:', error);
  }
}

// Main function
async function main() {
  console.log('=== Jishu Android Connection Setup ===');
  
  // Get local IP address
  const localIp = getLocalIpAddress();
  console.log(`\nDetected local IP address: ${localIp || 'Not found'}`);
  
  // Ask user for connection method
  rl.question('\nChoose connection method:\n1. USB with ADB reverse (recommended)\n2. WiFi (device and computer on same network)\n3. Android Emulator\nEnter choice (1-3): ', async (choice) => {
    try {
      switch (choice) {
        case '1':
          // USB with ADB reverse
          console.log('\nSetting up USB connection with ADB reverse...');
          console.log('Checking for connected devices...');
          
          try {
            const devices = execSync('adb devices').toString();
            console.log(devices);
            
            if (!devices.includes('device')) {
              console.log('No devices connected or device not authorized. Please check your USB connection and device settings.');
              break;
            }
            
            console.log('Setting up port forwarding...');
            execSync('adb reverse tcp:5000 tcp:5000');
            console.log('Port forwarding set up successfully!');
            
            // Update API URL to use localhost
            updateApiUrl('http://localhost:5000');
            
            console.log('\nStarting Flask backend server...');
            console.log('Please keep this terminal open and start the React Native app in another terminal.');
            execSync('cd backend && python run.py', { stdio: 'inherit' });
          } catch (error) {
            console.error('Error setting up ADB reverse:', error.message);
            console.log('\nTroubleshooting tips:');
            console.log('1. Make sure ADB is installed and in your PATH');
            console.log('2. Enable USB debugging on your device');
            console.log('3. Connect your device via USB and authorize the connection');
          }
          break;
          
        case '2':
          // WiFi connection
          rl.question(`\nEnter your computer's IP address (default: ${localIp}): `, (ip) => {
            const ipToUse = ip || localIp;
            if (!ipToUse) {
              console.log('No IP address provided or detected. Cannot continue.');
              rl.close();
              return;
            }
            
            // Update API URL to use the IP address
            updateApiUrl(`http://${ipToUse}:5000`);
            
            console.log('\nStarting Flask backend server...');
            console.log('Please keep this terminal open and start the React Native app in another terminal.');
            execSync('cd backend && python run.py', { stdio: 'inherit' });
          });
          break;
          
        case '3':
          // Android Emulator
          console.log('\nSetting up connection for Android Emulator...');
          
          // Update API URL to use ******** (special IP for Android Emulator)
          updateApiUrl('http://********:5000');
          
          console.log('\nStarting Flask backend server...');
          console.log('Please keep this terminal open and start the React Native app in another terminal.');
          execSync('cd backend && python run.py', { stdio: 'inherit' });
          break;
          
        default:
          console.log('Invalid choice. Please run the script again and select a valid option.');
          rl.close();
      }
    } catch (error) {
      console.error('Error:', error.message);
      rl.close();
    }
  });
}

// Run the main function
main().catch(console.error);
