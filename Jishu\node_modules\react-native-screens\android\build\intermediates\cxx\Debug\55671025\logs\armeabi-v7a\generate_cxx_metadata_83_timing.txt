# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
generate_cxx_metadata completed in 27ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 44ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 52ms
  [gap of 27ms]
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 17ms
generate_cxx_metadata completed in 36ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 12ms
  [gap of 18ms]
generate_cxx_metadata completed in 34ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 34ms
generate_cxx_metadata completed in 55ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 22ms
generate_cxx_metadata completed in 43ms

