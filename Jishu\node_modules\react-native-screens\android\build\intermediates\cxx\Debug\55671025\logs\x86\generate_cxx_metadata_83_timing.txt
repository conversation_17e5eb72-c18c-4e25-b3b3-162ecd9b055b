# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
generate_cxx_metadata completed in 24ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 22ms
  [gap of 11ms]
generate_cxx_metadata completed in 44ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 16ms
generate_cxx_metadata completed in 32ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 27ms
generate_cxx_metadata completed in 37ms

# C/C++ build system timings
generate_cxx_metadata 24ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 18ms
generate_cxx_metadata completed in 41ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 31ms
  [gap of 10ms]
generate_cxx_metadata completed in 58ms

