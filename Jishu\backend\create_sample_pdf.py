#!/usr/bin/env python3
"""
Create a sample PDF with educational content for testing the RAG system
"""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import os

def create_sample_pdf():
    """Create a sample PDF with NEET Biology content"""
    
    # Create the pdfs directory if it doesn't exist
    pdf_dir = "pdfs"
    if not os.path.exists(pdf_dir):
        os.makedirs(pdf_dir)
    
    # Define the PDF file path
    pdf_path = os.path.join(pdf_dir, "NEET_Biology_Sample.pdf")
    
    # Create the PDF document
    doc = SimpleDocTemplate(pdf_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Create custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        spaceBefore=12
    )
    
    # Content for the PDF
    content = []
    
    # Title
    content.append(Paragraph("NEET Biology Study Guide", title_style))
    content.append(Spacer(1, 12))
    
    # Chapter 1: Cell Biology
    content.append(Paragraph("Chapter 1: Cell Biology", heading_style))
    
    cell_biology_text = """
    The cell is the fundamental unit of life. All living organisms are composed of one or more cells. 
    There are two main types of cells: prokaryotic and eukaryotic cells.
    
    Prokaryotic cells lack a membrane-bound nucleus and organelles. Examples include bacteria and archaea.
    The genetic material in prokaryotic cells is located in the nucleoid region.
    
    Eukaryotic cells have a membrane-bound nucleus and various organelles such as mitochondria, 
    endoplasmic reticulum, Golgi apparatus, and lysosomes. Examples include plant cells, animal cells, 
    and fungal cells.
    
    Key organelles and their functions:
    - Nucleus: Controls cell activities and contains DNA
    - Mitochondria: Powerhouse of the cell, produces ATP through cellular respiration
    - Ribosomes: Protein synthesis
    - Endoplasmic Reticulum: Transport system within the cell
    - Golgi Apparatus: Modifies and packages proteins
    - Lysosomes: Digestive organelles that break down waste materials
    """
    
    content.append(Paragraph(cell_biology_text, styles['Normal']))
    content.append(Spacer(1, 12))
    
    # Chapter 2: Photosynthesis
    content.append(Paragraph("Chapter 2: Photosynthesis", heading_style))
    
    photosynthesis_text = """
    Photosynthesis is the process by which green plants and some bacteria convert light energy, 
    usually from the sun, into chemical energy stored in glucose molecules.
    
    The overall equation for photosynthesis is:
    6CO₂ + 6H₂O + light energy → C₆H₁₂O₆ + 6O₂
    
    Photosynthesis occurs in two main stages:
    
    1. Light-dependent reactions (Photo-chemical phase):
    - Occur in the thylakoid membranes of chloroplasts
    - Chlorophyll absorbs light energy
    - Water molecules are split (photolysis)
    - ATP and NADPH are produced
    - Oxygen is released as a byproduct
    
    2. Light-independent reactions (Calvin Cycle):
    - Occur in the stroma of chloroplasts
    - CO₂ is fixed into organic molecules
    - ATP and NADPH from light reactions are used
    - Glucose is produced
    
    Factors affecting photosynthesis:
    - Light intensity
    - Carbon dioxide concentration
    - Temperature
    - Water availability
    - Chlorophyll content
    """
    
    content.append(Paragraph(photosynthesis_text, styles['Normal']))
    content.append(Spacer(1, 12))
    
    # Chapter 3: Human Physiology
    content.append(Paragraph("Chapter 3: Human Physiology - Respiratory System", heading_style))
    
    respiratory_text = """
    The respiratory system is responsible for gas exchange in the human body. It consists of:
    
    1. Upper Respiratory Tract:
    - Nose and nasal cavity
    - Pharynx (throat)
    - Larynx (voice box)
    
    2. Lower Respiratory Tract:
    - Trachea (windpipe)
    - Bronchi and bronchioles
    - Alveoli (air sacs)
    
    Process of Breathing:
    - Inspiration (inhalation): Diaphragm contracts and moves down, rib cage expands
    - Expiration (exhalation): Diaphragm relaxes and moves up, rib cage contracts
    
    Gas Exchange:
    - Occurs in the alveoli
    - Oxygen diffuses from alveoli into blood capillaries
    - Carbon dioxide diffuses from blood into alveoli
    - Hemoglobin in red blood cells carries oxygen to body tissues
    
    Respiratory Disorders:
    - Asthma: Inflammation and narrowing of airways
    - Pneumonia: Infection of the lungs
    - Tuberculosis: Bacterial infection affecting lungs
    - Emphysema: Damage to alveoli, reducing gas exchange efficiency
    """
    
    content.append(Paragraph(respiratory_text, styles['Normal']))
    content.append(Spacer(1, 12))
    
    # Study Tips
    content.append(Paragraph("Study Tips for NEET Biology", heading_style))
    
    study_tips_text = """
    1. Focus on NCERT textbooks as they form the foundation of NEET Biology
    2. Create detailed diagrams and flowcharts for complex processes
    3. Practice previous year questions regularly
    4. Make short notes for quick revision
    5. Use mnemonics to remember classifications and lists
    6. Understand concepts rather than rote memorization
    7. Take regular mock tests to assess your preparation
    8. Revise important topics multiple times
    """
    
    content.append(Paragraph(study_tips_text, styles['Normal']))
    
    # Build the PDF
    doc.build(content)
    print(f"Sample PDF created successfully: {pdf_path}")
    return pdf_path

if __name__ == "__main__":
    try:
        pdf_path = create_sample_pdf()
        print(f"✅ PDF created at: {pdf_path}")
    except ImportError as e:
        print("❌ Error: reportlab package not found.")
        print("Please install it using: pip install reportlab")
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
