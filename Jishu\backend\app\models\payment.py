from app import db
from datetime import datetime, timezone

class Purchase(db.Model):
    __tablename__ = 'purchases'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('users.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.<PERSON>Key('subjects.id'), nullable=False)
    purchase_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    max_retakes = db.Column(db.Integer, default=3)
    retake_used = db.Column(db.Integer, default=0)
    marks = db.Column(db.Integer, default=4)
    negative_marks = db.Column(db.Integer, default=1)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = db.relationship('User', backref=db.backref('purchases', lazy='dynamic'))
    subject = db.relationship('Subject', backref=db.backref('purchases', lazy='dynamic'))
    payments = db.relationship('Payment', secondary='payment_purchases', back_populates='purchases')

    def __repr__(self):
        return f'<Purchase {self.id} User:{self.user_id} Subject:{self.subject_id}>'

class Payment(db.Model):
    __tablename__ = 'payments'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(3), default='INR')
    payment_method = db.Column(db.String(50))
    transaction_id = db.Column(db.String(100))
    status = db.Column(db.String(20), default='pending')
    payment_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = db.relationship('User', backref=db.backref('payments', lazy='dynamic'))
    purchases = db.relationship('Purchase', secondary='payment_purchases', back_populates='payments')

    def __repr__(self):
        return f'<Payment {self.id} User:{self.user_id} Amount:{self.amount} Status:{self.status}>'

class PaymentPurchase(db.Model):
    __tablename__ = 'payment_purchases'

    payment_id = db.Column(db.Integer, db.ForeignKey('payments.id'), primary_key=True)
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchases.id'), primary_key=True)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

