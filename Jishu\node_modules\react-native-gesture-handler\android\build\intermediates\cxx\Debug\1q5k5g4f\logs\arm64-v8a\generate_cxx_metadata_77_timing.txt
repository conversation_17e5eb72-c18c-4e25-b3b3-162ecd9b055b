# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 24ms
  [gap of 16ms]
generate_cxx_metadata completed in 62ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 24ms
  [gap of 16ms]
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 10ms
  [gap of 13ms]
generate_cxx_metadata completed in 36ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 26ms
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 74ms

