"""
Migration to alter payment-related tables
"""

import pymysql
import os
import sys
from dotenv import load_dotenv

def upgrade():
    """Alter payment tables with necessary changes"""
    print("Starting payment tables migration...")

    # Load environment variables from .env file
    load_dotenv()

    # Get database connection details from environment variables
    database_url = os.getenv('DATABASE_URL', 'mysql+pymysql://root:admin@localhost/jishu')

    # Parse the database URL
    # Format: mysql+pymysql://username:password@host/database
    db_parts = database_url.replace('mysql+pymysql://', '').split('@')
    auth_parts = db_parts[0].split(':')
    host_db_parts = db_parts[1].split('/')

    db_user = auth_parts[0]
    db_password = auth_parts[1]
    db_host = host_db_parts[0]
    db_name = host_db_parts[1]

    print(f"Using database credentials: user={db_user}, host={db_host}, db={db_name}")

    print(f"Connecting to database {db_name} on {db_host}...")

    try:
        # Connect to the database
        connection = pymysql.connect(
            host=db_host,
            user=db_user,
            password=db_password,
            database=db_name
        )

        print("Connected to database successfully!")

        with connection.cursor() as cursor:
            print("Checking if tables exist...")

            # List all tables
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            print(f"Existing tables: {tables}")

            # Check if payments table exists
            if 'payments' not in tables:
                print("Error: payments table does not exist!")
                return

            # Check if payment_purchases table exists
            if 'payment_purchases' not in tables:
                print("Warning: payment_purchases table does not exist! Creating it...")
                cursor.execute('''
                CREATE TABLE payment_purchases (
                    payment_id INT NOT NULL,
                    purchase_id INT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    discount_amount DECIMAL(10,2) DEFAULT 0.00,
                    tax_amount DECIMAL(10,2) DEFAULT 0.00,
                    item_description VARCHAR(255),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (payment_id, purchase_id),
                    FOREIGN KEY (payment_id) REFERENCES payments(id),
                    FOREIGN KEY (purchase_id) REFERENCES purchases(id),
                    INDEX idx_payment (payment_id),
                    INDEX idx_purchase (purchase_id)
                )
                ''')

            print("Altering payments table...")

            # Check if columns exist in payments table
            cursor.execute("DESCRIBE payments")
            existing_columns = [col[0] for col in cursor.fetchall()]
            print(f"Existing columns in payments table: {existing_columns}")

            # Add payment_gateway column if it doesn't exist
            if 'payment_gateway' not in existing_columns:
                cursor.execute("ALTER TABLE payments ADD COLUMN payment_gateway VARCHAR(50)")
                print("Added payment_gateway column")

            # Add gateway_response column if it doesn't exist
            if 'gateway_response' not in existing_columns:
                cursor.execute("ALTER TABLE payments ADD COLUMN gateway_response TEXT")
                print("Added gateway_response column")

            # Add refund_status column if it doesn't exist
            if 'refund_status' not in existing_columns:
                cursor.execute("ALTER TABLE payments ADD COLUMN refund_status VARCHAR(20) DEFAULT 'none'")
                print("Added refund_status column")

            # Add refund_amount column if it doesn't exist
            if 'refund_amount' not in existing_columns:
                cursor.execute("ALTER TABLE payments ADD COLUMN refund_amount DECIMAL(10,2) DEFAULT 0.00")
                print("Added refund_amount column")

            # Add refund_date column if it doesn't exist
            if 'refund_date' not in existing_columns:
                cursor.execute("ALTER TABLE payments ADD COLUMN refund_date DATETIME")
                print("Added refund_date column")

            # Add indexes
            try:
                cursor.execute("ALTER TABLE payments ADD INDEX idx_transaction_id (transaction_id)")
                print("Added idx_transaction_id index")
            except Exception as e:
                print(f"Note: {str(e)}")

            try:
                cursor.execute("ALTER TABLE payments ADD INDEX idx_payment_date (payment_date)")
                print("Added idx_payment_date index")
            except Exception as e:
                print(f"Note: {str(e)}")
            print("Payments table altered successfully!")

            print("Altering payment_purchases table...")
            # Add new columns to payment_purchases table
            cursor.execute('''
            ALTER TABLE payment_purchases
            ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0.00,
            ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(10,2) DEFAULT 0.00,
            ADD COLUMN IF NOT EXISTS item_description VARCHAR(255)
            ''')
            print("Payment_purchases table altered successfully!")

            # Commit the changes
            connection.commit()
            print("All payment tables altered successfully!")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        if 'connection' in locals() and connection:
            connection.close()
            print("Database connection closed.")

if __name__ == "__main__":
    upgrade()
