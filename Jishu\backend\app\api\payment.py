from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.api import bp
from app.models.payment import Purchase
from app.models.exam import Subject
from datetime import datetime, timezone

@bp.route('/user/exams/purchase', methods=['POST'])
def purchase_exam():
    """Purchase a new exam for the current user"""
    try:
        # For development, make this endpoint accessible without JWT
        # In a real app, you would use @jwt_required() and get_jwt_identity()

        # Try to get the JWT identity if available
        try:
            from flask_jwt_extended import get_jwt_identity
            current_user_id = get_jwt_identity()
            print(f"JWT identity found: {current_user_id}")
        except Exception as jwt_error:
            print(f"JWT error: {str(jwt_error)}")
            # For development, use a default user ID
            current_user_id = 1
            print(f"Using default user ID: {current_user_id}")

        if not current_user_id:
            return jsonify({'error': 'Invalid user identity'}), 401

        data = request.get_json() or {}

        if 'subject_id' not in data:
            return jsonify({'error': 'Subject ID is required'}), 400

        subject_id = data['subject_id']

        # Check if subject exists
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'error': f'Subject with ID {subject_id} not found'}), 404

        # Check if user already purchased this subject
        existing_purchase = Purchase.query.filter_by(
            user_id=current_user_id,
            subject_id=subject_id
        ).first()

        if existing_purchase:
            # Return the existing purchase
            return jsonify({
                'message': 'You have already purchased this exam',
                'exam': {
                    'id': existing_purchase.id,
                    'user_id': existing_purchase.user_id,
                    'subject_id': existing_purchase.subject_id,
                    'subject': {
                        'id': subject.id,
                        'name': subject.subject_name,
                        'description': subject.description,
                        'category_id': subject.exam_category_id,
                        'category_name': subject.category.name if subject.category else None
                    },
                    'purchased_at': existing_purchase.purchase_date.isoformat() if existing_purchase.purchase_date else None,
                    'max_retakes': existing_purchase.max_retakes,
                    'retakes_used': existing_purchase.retake_used,
                    'retakes_remaining': existing_purchase.max_retakes - existing_purchase.retake_used
                }
            })

        # Create a new purchase
        purchase = Purchase(
            user_id=current_user_id,
            subject_id=subject_id,
            purchase_date=datetime.now(timezone.utc),
            max_retakes=data.get('max_retakes', 3),
            retake_used=0,
            marks=data.get('marks', 4),
            negative_marks=data.get('negative_marks', 1)
        )

        db.session.add(purchase)
        db.session.commit()

        return jsonify({
            'message': 'Exam purchased successfully',
            'exam': {
                'id': purchase.id,
                'user_id': purchase.user_id,
                'subject_id': purchase.subject_id,
                'subject': {
                    'id': subject.id,
                    'name': subject.subject_name,
                    'description': subject.description,
                    'category_id': subject.exam_category_id,
                    'category_name': subject.category.name if subject.category else None
                },
                'purchased_at': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
                'max_retakes': purchase.max_retakes,
                'retakes_used': purchase.retake_used,
                'retakes_remaining': purchase.max_retakes - purchase.retake_used
            }
        }), 201
    except Exception as e:
        db.session.rollback()
        print(f"Error in purchase_exam: {str(e)}")
        return jsonify({'error': 'An error occurred while purchasing the exam', 'details': str(e)}), 500
