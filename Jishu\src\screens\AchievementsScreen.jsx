import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, FlatList, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const AchievementsScreen = ({ route }) => {
  const navigation = useNavigation();
  const [attendedExams, setAttendedExams] = useState([]);
  const [purchasedExams, setPurchasedExams] = useState([]);

  useEffect(() => {
    loadAttendedExams();
    loadPurchasedExams();
  }, []);

  useEffect(() => {
    if (route.params?.attendedExam) {
      saveAttendedExam(route.params.attendedExam);
      // Refresh purchased exams when a new exam is completed
      loadPurchasedExams();
    }
  }, [route.params?.attendedExam]);

  // Load attended exams from AsyncStorage
  const loadAttendedExams = async () => {
    try {
      const storedExams = await AsyncStorage.getItem('attendedExams');
      if (storedExams) {
        setAttendedExams(JSON.parse(storedExams));
      }
    } catch (error) {
      console.error('Error loading attended exams:', error);
    }
  };

  // Load purchased exams from AsyncStorage
  const loadPurchasedExams = async () => {
    try {
      const storedExams = await AsyncStorage.getItem('subscribedExams');
      if (storedExams) {
        const exams = JSON.parse(storedExams);
        console.log('Loaded purchased exams:', exams);
        setPurchasedExams(exams);
      }
    } catch (error) {
      console.error('Error loading purchased exams:', error);
    }
  };

  // Save attended exams persistently
  const saveAttendedExam = async (newExam) => {
    try {
      const updatedExams = [...attendedExams, newExam];
      await AsyncStorage.setItem('attendedExams', JSON.stringify(updatedExams));
      setAttendedExams(updatedExams);
    } catch (error) {
      console.error('Error saving attended exam:', error);
    }
  };

  const handleRetake = (exam) => {
    // Check if exam is locked
    if (exam.isLocked || exam.status === 'generating' || exam.status === 'error') {
      // Show alert based on status
      if (exam.status === 'generating') {
        Alert.alert(
          "Questions Being Generated",
          "Our AI Teacher is still preparing your questions. Please check back later.",
          [{ text: "OK" }]
        );
      } else if (exam.status === 'error') {
        Alert.alert(
          "Error Generating Questions",
          "There was an error generating questions for this exam. Please try again later.",
          [{ text: "OK" }]
        );
      } else {
        Alert.alert(
          "Exam Locked",
          "This exam is currently locked. Please check back later.",
          [{ text: "OK" }]
        );
      }
      return;
    }

    // Check if this is a purchased exam with subjectId
    if (exam.subjectId) {
      navigation.navigate('ExamQuestionsScreen', {
        subject: exam.title || exam.subject,
        examId: exam.id,
        subjectId: exam.subjectId,
        category: exam.category,
        categoryName: exam.categoryName,
        isRetake: true
      });
    } else {
      // For older exams without subjectId
      navigation.navigate('ExamQuestionsScreen', {
        subject: exam.subject
      });
    }
  };

  // Format time (seconds) to MM:SS format
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Calculate percentage
  const calculatePercentage = (score, total) => {
    if (!total) return 0;
    return Math.round((score / total) * 100);
  };

  // Determine if the current exam is the one just completed
  const isCurrentExam = (exam, index) => {
    return route.params?.attendedExam &&
           exam.subject === route.params.attendedExam.subject &&
           index === attendedExams.length - 1;
  };

  // Navigate to dashboard
  const navigateToDashboard = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainTabs' }],
    });
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.homeButton}
        onPress={navigateToDashboard}
      >
        <MaterialIcons name="home" size={24} color="#fff" />
      </TouchableOpacity>

      {route.params?.attendedExam && (
        <View style={styles.currentExamCard}>
          <Text style={styles.currentExamTitle}>{route.params.attendedExam.subject}</Text>
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Score:</Text>
            <Text style={styles.resultValue}>
              {route.params.attendedExam.score} / {route.params.attendedExam.totalQuestions}
              <Text style={styles.percentageText}>
                {' '}({calculatePercentage(route.params.attendedExam.score, route.params.attendedExam.totalQuestions)}%)
              </Text>
            </Text>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{route.params.attendedExam.correctAnswers}</Text>
              <Text style={styles.statLabel}>Correct</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{route.params.attendedExam.wrongAnswers}</Text>
              <Text style={styles.statLabel}>Wrong</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{route.params.attendedExam.unattempted}</Text>
              <Text style={styles.statLabel}>Unattempted</Text>
            </View>
          </View>

          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Time Taken:</Text>
            <Text style={styles.resultValue}>{formatTime(route.params.attendedExam.timeTaken)}</Text>
          </View>

          <TouchableOpacity
            style={styles.retakeButton}
            onPress={() => handleRetake(route.params.attendedExam)}
          >
            <Text style={styles.retakeText}>Retake Exam</Text>
          </TouchableOpacity>
        </View>
      )}

      <Text style={styles.subHeader}>Your Exam History</Text>

      {purchasedExams.length === 0 && attendedExams.length === 0 ? (
        <Text style={styles.emptyText}>No exam history available.</Text>
      ) : (
        <FlatList
          data={[...purchasedExams, ...attendedExams.filter((exam, index) => !isCurrentExam(exam, index))]}
          keyExtractor={(item, index) => (item.id ? item.id.toString() : 'attended-' + index)}
          style={styles.examListContainer}
          renderItem={({ item }) => {
            // Determine if this is a purchased exam or an attended exam
            const isPurchased = item.hasOwnProperty('subjectId');
            const examName = isPurchased ? item.title : item.subject;
            const attemptsLeft = isPurchased ? 3 - (item.attemptCount || 0) : null;

            // Check if exam is locked (AI is still generating questions)
            const isLocked = isPurchased && (item.isLocked || item.status === 'generating' || item.status === 'error');

            // Get badge text based on status
            const getBadgeText = () => {
              if (isLocked) {
                if (item.status === 'generating') return 'Generating questions...';
                if (item.status === 'error') return 'Generation failed';
                return 'Locked';
              }
              return attemptsLeft > 0 ? `${attemptsLeft} attempts left` : 'No attempts left';
            };

            return (
              <View style={styles.examCard}>
                <View style={styles.examCardHeader}>
                  <Text style={styles.examName}>
                    {examName}
                    {isLocked && (
                      <MaterialIcons name="lock" size={16} color="#FFC107" style={{ marginLeft: 8 }} />
                    )}
                  </Text>
                  {isPurchased && (
                    <View style={[
                      styles.badgeContainer,
                      isLocked ? styles.badgeLocked : attemptsLeft <= 0 ? styles.badgeDisabled : styles.badgeActive
                    ]}>
                      <Text style={[
                        styles.badgeText,
                        isLocked && { color: '#FFC107' }
                      ]}>
                        {getBadgeText()}
                      </Text>
                    </View>
                  )}
                </View>

                <View style={styles.detailsContainer}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Score:</Text>
                    <Text style={styles.detailValue}>
                      {item.score} / {item.totalQuestions}
                      <Text style={styles.percentageText}>
                        {' '}({calculatePercentage(item.score, item.totalQuestions)}%)
                      </Text>
                    </Text>
                  </View>

                  {item.lastAttemptDate && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Last Attempt:</Text>
                      <Text style={styles.detailValue}>{new Date(item.lastAttemptDate).toLocaleDateString()}</Text>
                    </View>
                  )}

                  {isPurchased && item.hasOwnProperty('correctAnswers') && (
                    <View style={styles.statsRow}>
                      <View style={styles.statItem}>
                        <Text style={styles.statValue}>{item.correctAnswers}</Text>
                        <Text style={styles.statLabel}>Correct</Text>
                      </View>
                      <View style={styles.statItem}>
                        <Text style={styles.statValue}>{item.wrongAnswers || 0}</Text>
                        <Text style={styles.statLabel}>Wrong</Text>
                      </View>
                      <View style={styles.statItem}>
                        <Text style={styles.statValue}>{item.unattempted || 0}</Text>
                        <Text style={styles.statLabel}>Skipped</Text>
                      </View>
                    </View>
                  )}
                </View>

                <TouchableOpacity
                  style={[
                    styles.retakeButton,
                    isPurchased && (isLocked
                      ? styles.lockedButton
                      : attemptsLeft <= 0 ? styles.disabledButton : null)
                  ]}
                  onPress={() => handleRetake(item)}
                  disabled={isPurchased && (isLocked || attemptsLeft <= 0)}
                >
                  <Text style={[
                    styles.retakeText,
                    isLocked && { color: '#FFC107' }
                  ]}>
                    {isLocked
                      ? (item.status === 'generating' ? 'Generating...' : 'Locked')
                      : isPurchased && attemptsLeft <= 0 ? 'No Attempts Left' : 'Take Test'}
                  </Text>
                  {isLocked && (
                    <MaterialIcons name="lock" size={16} color="#FFC107" style={{ marginLeft: 8 }} />
                  )}
                </TouchableOpacity>
              </View>
            );
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff'
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333'
  },
  homeButton: {
    backgroundColor: '#3461eb',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'absolute',
    bottom: 20,
    right: 20,
    zIndex: 100
  },
  subHeader: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 16,
    color: '#333',
    textAlign: 'center',
    letterSpacing: 0.5
  },
  currentExamCard: {
    padding: 20,
    backgroundColor: '#f0f8ff',
    borderRadius: 16,
    marginBottom: 24,
    elevation: 4,
    shadowColor: '#3461eb',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: '#e6f0ff'
  },
  currentExamTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#3461eb'
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10
  },
  resultLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#555'
  },
  resultValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333'
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12
  },
  statItem: {
    alignItems: 'center'
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#3461eb'
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4
  },
  card: {
    padding: 15,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginVertical: 6,
    elevation: 2
  },
  examName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 10
  },
  scoreRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 4
  },
  percentageText: {
    color: '#3461eb',
    fontWeight: '500'
  },
  retakeButton: {
    marginTop: 16,
    backgroundColor: '#3461eb',
    padding: 12,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#3461eb',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3
  },
  retakeText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 15,
    letterSpacing: 0.5
  },
  redirectText: {
    marginTop: 16,
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic'
  },
  emptyText: {
    color: '#666',
    fontStyle: 'italic',
    marginTop: 10
  },
  examListContainer: {
    flex: 1,
    marginBottom: 20
  },
  examCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 8,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4
  },
  examCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  badgeContainer: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center'
  },
  badgeActive: {
    backgroundColor: '#e6f0ff'
  },
  badgeDisabled: {
    backgroundColor: '#f0f0f0'
  },
  badgeLocked: {
    backgroundColor: '#FFF8E1'
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#3461eb'
  },
  detailsContainer: {
    marginVertical: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
    padding: 12
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  detailLabel: {
    fontSize: 14,
    color: '#555',
    fontWeight: '500'
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold'
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee'
  },
  disabledButton: {
    backgroundColor: '#ccc'
  },
  lockedButton: {
    backgroundColor: '#3461eb20',
    borderWidth: 1,
    borderColor: '#FFC107'
  }
});

export default AchievementsScreen;
