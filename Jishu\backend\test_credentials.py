import pymysql

# Try different combinations of username and password
credentials = [
    ('root', ''),           # Empty password
    ('root', 'root'),       # Default password
    ('root', 'password'),   # Common password
    ('admin', 'admin'),     # Common admin credentials
    ('admin', 'password'),  # Another common combination
    ('jishu', 'jishu'),     # Application name as credentials
    ('jishu_user', 'jishu_password'), # Application-specific credentials
    ('mysql', 'mysql'),     # MySQL default
    ('', ''),               # No credentials
]

for username, password in credentials:
    try:
        # Try to connect directly with PyMySQL
        connection = pymysql.connect(
            host='localhost',
            user=username,
            password=password,
            database='jishu'
        )

        print(f"Successfully connected with username: {username}, password: {password}")
        connection.close()
        break
    except Exception as e:
        print(f"Failed to connect with username: {username}, password: {password}")
        print(f"Error: {e}")
        print("---")
