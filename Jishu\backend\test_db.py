import pymysql

# This is needed to make SQLAlchemy use PyMySQL instead of MySQLdb
pymysql.install_as_MySQLdb()

from app import create_app, db

app = create_app()

with app.app_context():
    try:
        # Try to execute a simple query
        with db.engine.connect() as conn:
            result = conn.execute(db.text("SELECT 1"))
            print("Database connection successful!")
    except Exception as e:
        print(f"Error connecting to database: {e}")
