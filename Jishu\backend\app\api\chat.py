from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.api import bp
from app.models.user import User
from app.models.chat import ChatSession, ChatMessage
from datetime import datetime

@bp.route('/chat/sessions', methods=['GET'])
@jwt_required()
def get_chat_sessions():
    """Get all chat sessions for the current user"""
    current_user_id = get_jwt_identity()

    # Get chat sessions for the current user
    sessions = ChatSession.query.filter_by(user_id=current_user_id, is_active=True).order_by(
        ChatSession.updated_at.desc()).all()

    return jsonify([session.to_dict() for session in sessions])

@bp.route('/chat/sessions/<int:session_id>', methods=['GET'])
@jwt_required()
def get_chat_session(session_id):
    """Get a specific chat session with its messages"""
    current_user_id = get_jwt_identity()

    # Get the chat session
    session = ChatSession.query.filter_by(id=session_id, user_id=current_user_id).first_or_404()

    # Get messages for this session
    messages = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.created_at).all()

    return jsonify({
        'session': session.to_dict(),
        'messages': [message.to_dict() for message in messages]
    })

@bp.route('/chat/sessions', methods=['POST'])
@jwt_required()
def create_chat_session():
    """Create a new chat session"""
    current_user_id = get_jwt_identity()
    data = request.get_json() or {}

    # Create a new chat session
    session = ChatSession(
        user_id=current_user_id,
        title=data.get('title', 'New Chat'),
        subject_id=data.get('subject_id')
    )

    db.session.add(session)
    db.session.commit()

    return jsonify(session.to_dict()), 201

@bp.route('/chat/sessions/<int:session_id>/messages', methods=['POST'])
@jwt_required()
def add_chat_message(session_id):
    """Add a message to a chat session"""
    current_user_id = get_jwt_identity()
    data = request.get_json() or {}

    if 'message' not in data or not data['message'].strip():
        return jsonify({'error': 'Message is required'}), 400

    # Get the chat session
    session = ChatSession.query.filter_by(id=session_id, user_id=current_user_id).first_or_404()

    # Create a new message
    message = ChatMessage(
        session_id=session_id,
        user_id=current_user_id,
        is_bot=False,
        message=data['message']
    )

    # Update session's updated_at timestamp
    session.updated_at = datetime.utcnow()

    db.session.add(message)
    db.session.commit()

    # In a real app, you would now generate a bot response
    # For now, we'll create a mock bot response
    bot_message = ChatMessage(
        session_id=session_id,
        user_id=current_user_id,  # We still associate with the user, but mark as bot
        is_bot=True,
        message=f"This is an automated response to: {data['message']}"
    )

    db.session.add(bot_message)
    db.session.commit()

    return jsonify({
        'user_message': message.to_dict(),
        'bot_message': bot_message.to_dict()
    }), 201

@bp.route('/chat/personal', methods=['GET'])
@jwt_required(optional=True)
def get_personal_chats():
    """Get all personal chat conversations for the current user"""
    current_user_id = get_jwt_identity()

    # If no user is authenticated, return an empty list
    if current_user_id is None:
        return jsonify([]), 200

    # For personal chats, we'll get all users who have exchanged messages with the current user
    # This is a simplified approach - in a real app, you might have a separate table for conversations

    # Get all users who have sent messages to the current user
    chat_partners = db.session.query(User).join(
        ChatMessage, ChatMessage.user_id == User.id
    ).filter(
        ChatMessage.session_id.in_(
            db.session.query(ChatSession.id).filter_by(user_id=current_user_id)
        ),
        ChatMessage.user_id != current_user_id,
        ChatMessage.is_bot == False  # Only include human users, not bot messages
    ).distinct().all()

    # For demonstration purposes, if no chat partners are found, include some sample users
    if not chat_partners:
        # Get some sample users (excluding the current user)
        chat_partners = User.query.filter(User.id != current_user_id).limit(3).all()

    # Format the response with the last message for each conversation
    conversations = []
    for partner in chat_partners:
        # Get the last message exchanged with this user
        last_message = db.session.query(ChatMessage).join(
            ChatSession, ChatSession.id == ChatMessage.session_id
        ).filter(
            ChatSession.user_id.in_([current_user_id, partner.id]),
            ChatMessage.user_id.in_([current_user_id, partner.id])
        ).order_by(ChatMessage.created_at.desc()).first()

        # If no message exists, create a placeholder
        message_preview = "Start a conversation!" if not last_message else last_message.message
        if message_preview and len(message_preview) > 50:
            message_preview = message_preview[:50] + "..."

        # Count unread messages (simplified - in a real app, you'd track read status)
        unread_count = 0
        if last_message and last_message.user_id == partner.id:
            unread_count = 1

        conversations.append({
            'id': f"chat_{partner.id}",  # Create a unique ID for the conversation
            'user_id': partner.id,
            'user_name': partner.name,
            'user_avatar': partner.icon or 'https://randomuser.me/api/portraits/men/32.jpg',
            'last_message': message_preview,
            'unread_count': unread_count,
            'timestamp': last_message.created_at.isoformat() if last_message else datetime.utcnow().isoformat(),
            'is_creator': partner.role == 'admin'  # Assuming admin users are "creators"
        })

    return jsonify(conversations)

@bp.route('/chat/personal/<int:user_id>/messages', methods=['GET'])
@jwt_required(optional=True)
def get_personal_chat_messages(user_id):
    """Get messages exchanged with a specific user"""
    current_user_id = get_jwt_identity()

    # If no user is authenticated, return an empty list
    if current_user_id is None:
        return jsonify([]), 200

    # Get or create a chat session for these two users
    session = ChatSession.query.filter_by(
        user_id=current_user_id,
        title=f"Chat with {user_id}"
    ).first()

    if not session:
        # Create a new session if none exists
        other_user = User.query.get_or_404(user_id)
        session = ChatSession(
            user_id=current_user_id,
            title=f"Chat with {other_user.name}"
        )
        db.session.add(session)
        db.session.commit()

    # Get messages for this session
    messages = ChatMessage.query.filter_by(session_id=session.id).order_by(ChatMessage.created_at).all()

    # If no messages exist, create a welcome message
    if not messages:
        other_user = User.query.get_or_404(user_id)
        welcome_message = ChatMessage(
            session_id=session.id,
            user_id=user_id,
            is_bot=False,
            message=f"Hello! I'm {other_user.name}. How can I help you today?"
        )
        db.session.add(welcome_message)
        db.session.commit()
        messages = [welcome_message]

    return jsonify([message.to_dict() for message in messages])

@bp.route('/chat/personal/<int:user_id>/messages', methods=['POST'])
@jwt_required(optional=True)
def send_personal_chat_message(user_id):
    """Send a message to a specific user"""
    current_user_id = get_jwt_identity()

    # If no user is authenticated, return an error
    if current_user_id is None:
        return jsonify({'error': 'Authentication required'}), 401
    data = request.get_json() or {}

    if 'message' not in data or not data['message'].strip():
        return jsonify({'error': 'Message is required'}), 400

    # Get or create a chat session for these two users
    session = ChatSession.query.filter_by(
        user_id=current_user_id,
        title=f"Chat with {user_id}"
    ).first()

    if not session:
        # Create a new session if none exists
        other_user = User.query.get_or_404(user_id)
        session = ChatSession(
            user_id=current_user_id,
            title=f"Chat with {other_user.name}"
        )
        db.session.add(session)
        db.session.commit()

    # Create a new message
    message = ChatMessage(
        session_id=session.id,
        user_id=current_user_id,
        is_bot=False,
        message=data['message']
    )

    # Update session's updated_at timestamp
    session.updated_at = datetime.utcnow()

    db.session.add(message)
    db.session.commit()

    # In a real app with real-time features, you would notify the other user
    # For now, we'll create a mock response from the other user
    other_user = User.query.get_or_404(user_id)
    response_message = ChatMessage(
        session_id=session.id,
        user_id=user_id,
        is_bot=False,
        message=f"Thanks for your message! This is an automated response from {other_user.name}."
    )

    db.session.add(response_message)
    db.session.commit()

    return jsonify({
        'user_message': message.to_dict(),
        'response_message': response_message.to_dict()
    }), 201
