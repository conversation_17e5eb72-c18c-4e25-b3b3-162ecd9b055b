# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 32ms
  [gap of 12ms]
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 34ms
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 64ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 41ms
  [gap of 18ms]
generate_cxx_metadata completed in 78ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 17ms
  [gap of 11ms]
generate_cxx_metadata completed in 44ms

