# C/C++ build system timings
generate_cxx_metadata
  [gap of 58ms]
  create-invalidation-state 60ms
  [gap of 18ms]
  write-metadata-json-to-file 30ms
generate_cxx_metadata completed in 166ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 37ms
  [gap of 10ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 95ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 25ms
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 71ms

