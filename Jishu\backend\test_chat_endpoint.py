#!/usr/bin/env python3
"""
Test script for the new chat endpoint using Ollama
"""

import requests
import json
import sys

# Configuration
API_BASE_URL = "http://localhost:5000"
CHAT_ENDPOINT = f"{API_BASE_URL}/api/ai/chat"

def test_chat_endpoint():
    """Test the chat endpoint with a sample question"""
    
    # Test data
    test_data = {
        "question": "What are some effective study techniques for NEET preparation?",
        "chat_history": [],
        "timestamp": "2024-01-01T00:00:00Z"
    }
    
    print(f"Testing chat endpoint: {CHAT_ENDPOINT}")
    print(f"Sending question: {test_data['question']}")
    
    try:
        # Send POST request
        response = requests.post(
            CHAT_ENDPOINT,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"AI Response: {result.get('response', 'No response field')}")
            print(f"Model: {result.get('model', 'No model field')}")
            print(f"Timestamp: {result.get('timestamp', 'No timestamp field')}")
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the server.")
        print("Make sure the Flask server is running on localhost:5000")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: The request took too long.")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

def test_chat_with_history():
    """Test the chat endpoint with chat history"""
    
    # Test data with chat history
    test_data = {
        "question": "Can you give me more specific tips for Physics?",
        "chat_history": [
            {"role": "user", "content": "What are some effective study techniques for NEET preparation?"},
            {"role": "assistant", "content": "For NEET preparation, focus on NCERT books first, create a study schedule, and practice regularly with mock tests."}
        ],
        "timestamp": "2024-01-01T00:00:00Z"
    }
    
    print(f"\nTesting chat endpoint with history: {CHAT_ENDPOINT}")
    print(f"Sending question: {test_data['question']}")
    print(f"Chat history length: {len(test_data['chat_history'])}")
    
    try:
        # Send POST request
        response = requests.post(
            CHAT_ENDPOINT,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"AI Response: {result.get('response', 'No response field')}")
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the server.")
        print("Make sure the Flask server is running on localhost:5000")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: The request took too long.")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Chat Endpoint with Ollama")
    print("=" * 50)
    
    # Test basic chat
    test_chat_endpoint()
    
    # Test chat with history
    test_chat_with_history()
    
    print("\n" + "=" * 50)
    print("Test completed!")
