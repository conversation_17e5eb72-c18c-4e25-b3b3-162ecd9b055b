# MCQ Generation API Documentation

This document describes the MCQ (Multiple Choice Questions) generation endpoints that use Ollama for AI-powered question generation.

## Overview

The MCQ generation system supports two modes:
1. **PDF-based generation**: Uses content from PDF files in the `pdfs` directory
2. **General knowledge generation**: Uses <PERSON><PERSON><PERSON>'s general knowledge without PDF content

## Prerequisites

### Required Dependencies
- `ollama` - For AI model interaction
- `PyPDF2` - For PDF text extraction
- `sentence-transformers` - For text embeddings (optional)

### Installation
```bash
pip install ollama PyPDF2 sentence-transformers
```

### Ollama Setup
1. Install Ollama on your system
2. Pull the required model:
   ```bash
   ollama pull llama3.2:1b
   ```

## API Endpoints

### 1. AI Status Check
**Endpoint**: `GET /ai/status`

**Description**: Check the status of AI services and dependencies.

**Response**:
```json
{
  "status": "running",
  "dependencies": {
    "ollama": true,
    "PyPDF2": true,
    "sentence_transformers": true
  },
  "pdf_folder": "/path/to/pdfs",
  "pdf_folder_exists": true,
  "pdf_files_count": 5,
  "pdf_files": ["file1.pdf", "file2.pdf"]
}
```

### 2. Generate MCQ with Ollama
**Endpoint**: `POST /ai/generate-mcq-ollama`

**Description**: Generate MCQ questions using Ollama with optional PDF content.

**Request Body**:
```json
{
  "topic": "photosynthesis",
  "subject": "Biology",
  "num_questions": 5,
  "use_pdf_content": true
}
```

**Parameters**:
- `topic` (string, optional): The topic for questions (default: "general knowledge")
- `subject` (string, optional): The subject area
- `num_questions` (integer, optional): Number of questions to generate (1-20, default: 5)
- `use_pdf_content` (boolean, optional): Whether to use PDF content (default: true)

**Response (Success)**:
```json
{
  "status": "success",
  "questions": [
    {
      "question": "What is the primary function of chlorophyll in photosynthesis?",
      "options": [
        "A. To absorb light energy",
        "B. To produce oxygen",
        "C. To store glucose",
        "D. To transport water"
      ],
      "correct_answer": "A",
      "explanation": "Chlorophyll absorbs light energy which is essential for photosynthesis.",
      "difficulty": "medium"
    }
  ],
  "sources_used": ["biology_textbook.pdf"],
  "total_pdfs_processed": 3,
  "generation_method": "pdf_content"
}
```

### 3. Generate MCQ from PDFs
**Endpoint**: `GET|POST /ai/generate-mcq-from-pdfs`

**Description**: Generate MCQ questions specifically from PDF files in the pdfs directory.

**GET Parameters**:
- `num_questions` (integer, optional): Number of questions (default: 5)
- `topic` (string, optional): Topic focus
- `subject` (string, optional): Subject area

**POST Request Body**:
```json
{
  "num_questions": 5,
  "topic": "mathematics",
  "subject": "Math"
}
```

**Response**:
```json
{
  "status": "success",
  "questions": [...],
  "sources_used": ["math_book.pdf", "algebra_guide.pdf"],
  "total_pdfs_processed": 2,
  "total_sources_used_for_mcq": 2,
  "pdf_folder": "/path/to/pdfs"
}
```

## Error Responses

### Ollama Not Available
```json
{
  "status": "error",
  "message": "Ollama is not available. Please install the ollama package."
}
```

### Invalid Parameters
```json
{
  "status": "error",
  "message": "num_questions must be an integer between 1 and 20"
}
```

### No PDF Files Found
```json
{
  "status": "error",
  "message": "No PDF files found in the pdfs directory: /path/to/pdfs"
}
```

### Partial Success (Invalid JSON)
```json
{
  "status": "partial_success",
  "raw_content": "Generated content that couldn't be parsed as JSON",
  "warning": "Generated content may not be in valid JSON format",
  "generation_method": "general_knowledge"
}
```

## Usage Examples

### Python Example
```python
import requests
import json

# Generate MCQ using general knowledge
response = requests.post('http://localhost:5000/ai/generate-mcq-ollama', 
                        json={
                            "topic": "photosynthesis",
                            "subject": "Biology",
                            "num_questions": 3,
                            "use_pdf_content": False
                        })

if response.status_code == 200:
    data = response.json()
    if data['status'] == 'success':
        for i, question in enumerate(data['questions'], 1):
            print(f"Question {i}: {question['question']}")
            for option in question['options']:
                print(f"  {option}")
            print(f"Correct: {question['correct_answer']}")
            print(f"Explanation: {question['explanation']}")
            print()
```

### cURL Example
```bash
# Check AI status
curl -X GET http://localhost:5000/ai/status

# Generate MCQ questions
curl -X POST http://localhost:5000/ai/generate-mcq-ollama \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "algebra",
    "subject": "Mathematics",
    "num_questions": 2,
    "use_pdf_content": false
  }'
```

## PDF Setup

1. Create a `pdfs` directory in your project root
2. Add PDF files containing educational content
3. The system will automatically extract text from all PDF files
4. Use the PDF-based endpoints to generate questions from this content

## Notes

- The system uses the `llama3.2:1b` model by default
- Questions are generated in JSON format with specific structure
- PDF content is chunked to fit within model context limits
- The system gracefully falls back to general knowledge if PDF processing fails
- All endpoints support CORS for web application integration
