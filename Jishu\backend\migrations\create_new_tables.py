"""
Migration to create new table structure
"""

from app import create_app, db

def upgrade():
    """Create new tables"""
    app = create_app()
    with app.app_context():
        # Create users table
        db.engine.execute('''
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VA<PERSON>HAR(100),
            mobile_number VARCHAR(15) NOT NULL UNIQUE,
            icon VARCHAR(255),
            role VARCHAR(20) DEFAULT 'user',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            is_profile_complete BOOLEAN DEFAULT FALSE,
            INDEX idx_mobile_number (mobile_number)
        )
        ''')

        # Create exam_categories table
        db.engine.execute('''
        CREATE TABLE exam_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create subjects table
        db.engine.execute('''
        CREATE TABLE subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            exam_category_id INT NOT NULL,
            subject_name VARCHAR(100) NOT NULL,
            description TEXT,
            max_retakes INT DEFAULT 3,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (exam_category_id) REFERENCES exam_categories(id),
            INDEX idx_exam_category (exam_category_id)
        )
        ''')

        # Create community_posts table
        db.engine.execute('''
        CREATE TABLE community_posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content TEXT NOT NULL,
            image_url VARCHAR(255),
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_user_id (user_id)
        )
        ''')

        # Create community_comments table
        db.engine.execute('''
        CREATE TABLE community_comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            post_id INT NOT NULL,
            user_id INT NOT NULL,
            content TEXT NOT NULL,
            likes_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES community_posts(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_post_id (post_id),
            INDEX idx_user_id (user_id)
        )
        ''')

        # Create payments table first (removing purchase_id reference for now)
        db.engine.execute('''
        CREATE TABLE payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'INR',
            payment_method VARCHAR(50),
            transaction_id VARCHAR(100),
            status VARCHAR(20) DEFAULT 'pending',
            payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status)
        )
        ''')

        # Create purchases table
        db.engine.execute('''
        CREATE TABLE purchases (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            subject_id INT NOT NULL,
            purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            max_retakes INT DEFAULT 3,
            retake_used INT DEFAULT 0,
            marks INT DEFAULT 4,
            negative_marks INT DEFAULT 1,
            payment_id INT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (subject_id) REFERENCES subjects(id),
            FOREIGN KEY (payment_id) REFERENCES payments(id),
            INDEX idx_user_subject (user_id, subject_id)
        )
        ''')

        # Create questions table
        db.engine.execute('''
        CREATE TABLE questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subject_id INT NOT NULL,
            exam_category_id INT NOT NULL,
            user_id INT,
            purchase_id INT,
            question_text TEXT NOT NULL,
            option_a TEXT NOT NULL,
            option_b TEXT NOT NULL,
            option_c TEXT NOT NULL,
            option_d TEXT NOT NULL,
            correct_option CHAR(1) NOT NULL,
            explanation TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (subject_id) REFERENCES subjects(id),
            FOREIGN KEY (exam_category_id) REFERENCES exam_categories(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (purchase_id) REFERENCES purchases(id),
            INDEX idx_subject (subject_id),
            INDEX idx_exam_category (exam_category_id)
        )
        ''')

if __name__ == "__main__":
    upgrade()

