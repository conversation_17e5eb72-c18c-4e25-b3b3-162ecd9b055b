import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, ActivityIndicator, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { subjectsAPI } from '../services/api';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
};

const QuestionTypeSelectionScreen = ({ route }) => {
  const { categoryId, categoryName } = route.params;
  const navigation = useNavigation();
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Map subject names to appropriate icons
  const getIconForSubject = (subjectName) => {
    const name = subjectName.toLowerCase();
    if (name.includes('physics')) return 'atom';
    if (name.includes('chemistry')) return 'flask-empty-outline';
    if (name.includes('biology') || name.includes('botany')) return 'virus-outline';
    if (name.includes('zoology')) return 'dna';
    if (name.includes('math')) return 'calculator-variant-outline';
    if (name.includes('full') || name.includes('mock')) return 'stethoscope';
    if (name.includes('computer')) return 'laptop';
    if (name.includes('electronics')) return 'chip';
    return 'book-open-variant'; // Default icon
  };

  // Fetch subjects for the selected category
  useEffect(() => {
    fetchSubjects();
  }, [categoryId, categoryName]);

  const fetchSubjects = async () => {
    setLoading(true);
    setError(null);
    try {
      // Log the received categoryId for debugging
      console.log('Received categoryId:', categoryId, 'Type:', typeof categoryId);

      // Ensure categoryId is valid and properly formatted
      let categoryIdParam;
      if (typeof categoryId === 'number') {
        categoryIdParam = categoryId;
      } else if (typeof categoryId === 'string' && !isNaN(Number(categoryId))) {
        categoryIdParam = Number(categoryId);
      } else {
        console.warn('Invalid categoryId received:', categoryId);
        setError('Invalid category ID');
        setLoading(false);
        return;
      }

      console.log('Using categoryIdParam for API call:', categoryIdParam);

      // Set the screen title to the category name
      navigation.setOptions({
        title: categoryName || 'Select Subject'
      });

      // Fetch subjects for the selected category
      const response = await subjectsAPI.getByCategory(categoryIdParam);
      console.log('Subjects fetched:', response);

      if (Array.isArray(response) && response.length > 0) {
        // Format the subjects data
        const formattedSubjects = response.map(subject => ({
          id: subject.id,
          name: subject.name,
          description: subject.description,
          icon: getIconForSubject(subject.name),
          max_retakes: subject.max_retakes || 3,
          category_id: subject.category_id,
          category_name: subject.category_name || categoryName
        }));

        setSubjects(formattedSubjects);
      } else {
        setSubjects([]);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching subjects:', err);
      setError('Failed to load subjects. Please try again.');
      setSubjects([]);
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const onPress = (subject) => {
    // Navigate to test instructions with the subject details
    console.log('Navigating to TestInstructions with subject:', subject);
    navigation.navigate('TestInstructions', {
      subjectId: subject.id,
      subjectName: subject.name,
      categoryId: categoryId,
      categoryName: categoryName,
      isFullMock: subject.name.toLowerCase().includes('full mock')
    });
  };

  return (
    <SafeAreaView style={styles.container}>

      <View style={styles.subjectsContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading subjects...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchSubjects}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : subjects.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No subjects available for this category.</Text>
          </View>
        ) : (
          subjects.map((item, index) => (
            <TouchableOpacity key={index} style={styles.card} onPress={() => onPress(item)}>
              <View style={styles.iconContainer}>
                <MaterialCommunityIcons name={item.icon} size={24} color="#fff" />
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.title}>{item.name}</Text>
                <Text style={styles.subtitle}>
                  {item.isFullMock ? 'Complete exam pattern' : `${categoryName || categoryId.replace('_', ' ')} subject test`}
                </Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color={COLORS.textSecondary} />
            </TouchableOpacity>
          ))
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    backgroundColor: COLORS.card,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  subjectsContainer: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    backgroundColor: COLORS.background,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.card,
    padding: 15,
    marginVertical: 6,
    marginHorizontal: 10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
    width: '90%',
  },
  iconContainer: {
    backgroundColor: COLORS.primary,
    padding: 12,
    borderRadius: 8,
    marginRight: 10,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  subtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default QuestionTypeSelectionScreen;
