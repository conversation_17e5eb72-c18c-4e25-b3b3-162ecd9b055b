import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { completeProfile } from '../../store/slices/authSlice';
import ProfileCompletionModal from './ProfileCompletionModal';

/**
 * This component handles the profile completion flow.
 * It checks if the user's profile is incomplete and shows the profile completion modal if needed.
 */
const ProfileCompletionHandler = () => {
  const { isAuthenticated, user, isProfileComplete } = useSelector((state) => state.auth);
  const [showModal, setShowModal] = useState(false);
  const dispatch = useDispatch();

  // Check if we need to show the profile completion modal
  useEffect(() => {
    if (isAuthenticated && user && !isProfileComplete) {
      console.log('User needs to complete profile:', user);
      setShowModal(true);
    } else {
      setShowModal(false);
    }
  }, [isAuthenticated, user, isProfileComplete]);

  // Handle modal close
  const handleModalClose = () => {
    // Allow closing the modal, but show a warning if profile is not complete
    if (!isProfileComplete) {
      console.log('Warning: Closing profile completion modal without completing profile');
    }
    setShowModal(false);
  };

  // Handle profile completion
  const handleCompleteProfile = async (profileData) => {
    try {
      console.log('ProfileCompletionHandler: Handling profile completion with data:', profileData);

      // Make sure we have the mobile number
      if (!profileData.mobile_number && user?.mobile_number) {
        profileData.mobile_number = user.mobile_number;
        console.log('Added mobile number from user state:', user.mobile_number);
      }

      // Validate data before dispatching
      if (!profileData.name) {
        throw new Error('Name is required');
      }
      if (!profileData.avatar) {
        throw new Error('Avatar is required');
      }
      if (!profileData.mobile_number) {
        throw new Error('Mobile number is required');
      }

      console.log('ProfileCompletionHandler: Dispatching completeProfile with data:', profileData);

      // Dispatch the action
      const resultAction = await dispatch(completeProfile(profileData));
      console.log('ProfileCompletionHandler: Result action:', resultAction);

      if (resultAction.error) {
        throw new Error(resultAction.error.message || 'Failed to complete profile');
      }

      console.log('ProfileCompletionHandler: Profile completed successfully');
      return true;
    } catch (error) {
      console.error('Error in handleCompleteProfile:', error);
      return false;
    }
  };

  return (
    <ProfileCompletionModal
      visible={showModal}
      onClose={handleModalClose}
      onCompleteProfile={handleCompleteProfile}
      user={user}
    />
  );
};

export default ProfileCompletionHandler;
