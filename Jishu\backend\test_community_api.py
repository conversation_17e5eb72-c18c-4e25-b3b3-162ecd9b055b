import requests
import json

def test_create_post():
    url = 'http://localhost:5000/api/community/posts'
    data = {
        'content': 'This is a test post from Python script',
        'title': 'Test Post'
    }
    
    print(f"Sending POST request to {url} with data: {data}")
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            print(f"Response: {response.json()}")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def test_get_posts():
    url = 'http://localhost:5000/api/community/posts'
    
    print(f"Sending GET request to {url}")
    
    try:
        response = requests.get(url)
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Total posts: {result.get('total', 'N/A')}")
            posts = result.get('posts', [])
            print(f"Number of posts returned: {len(posts)}")
            if posts:
                print("First post:")
                print(f"  ID: {posts[0].get('id')}")
                print(f"  Content: {posts[0].get('content')[:50]}...")
                print(f"  User: {posts[0].get('user_name')}")
                print(f"  Likes: {posts[0].get('likes_count')}")
                print(f"  Comments: {posts[0].get('comments_count')}")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    print("Testing community API endpoints...")
    test_get_posts()
    test_create_post()
    print("Testing complete!")
