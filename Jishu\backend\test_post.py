import requests
import json

def test_post_request():
    url = 'http://localhost:5000/test-post'
    data = {
        'content': 'This is a test post',
        'title': 'Test Post'
    }

    print(f"Sending POST request to {url} with data: {data}")

    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    test_post_request()
