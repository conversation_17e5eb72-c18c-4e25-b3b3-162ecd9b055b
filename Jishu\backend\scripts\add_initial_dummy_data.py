"""
<PERSON><PERSON><PERSON> to add initial dummy data to the first 8 tables in the Jishu application.
"""

from app import create_app, db
from app.models.user import User
from app.models.exam import ExamCategory, Subject, QuestionWithOptions
from app.models.user_progress import UserExam, ExamAttempt, UserAnswer
from app.models.payment import Payment
from datetime import datetime, timedelta
from decimal import Decimal
import random

def add_initial_dummy_data():
    """Add dummy data to first 8 tables"""
    app = create_app()
    with app.app_context():
        print("Adding initial dummy data to the database...")

        # Check if data already exists
        if db.session.query(User).count() > 0:
            print("Users already exist in the database. Skipping user creation.")
            users = db.session.query(User).all()
        else:
            # Add users
            users = add_users()

        # Check if categories already exist
        if db.session.query(ExamCategory).count() > 0:
            print("Exam categories already exist in the database. Skipping category creation.")
            categories = db.session.query(ExamCategory).all()
            subjects = db.session.query(Subject).all()
        else:
            # Add exam categories and subjects
            categories, subjects = add_exam_categories_and_subjects()

        # Check if questions already exist
        if db.session.query(QuestionWithOptions).count() > 0:
            print("Questions already exist in the database. Skipping question creation.")
            questions = db.session.query(QuestionWithOptions).all()
        else:
            # Add questions
            questions = add_questions_with_options(subjects)

        # Check if user exams already exist
        if db.session.query(UserExam).count() > 0:
            print("User exams already exist in the database. Skipping user progress creation.")
            user_exams = db.session.query(UserExam).all()
            exam_attempts = db.session.query(ExamAttempt).all()
            user_answers = db.session.query(UserAnswer).all()
        else:
            # Add user exams and attempts
            user_exams, exam_attempts, user_answers = add_user_progress(users, subjects, questions)

        # Check if payments already exist
        if db.session.query(Payment).count() > 0:
            print("Payments already exist in the database. Skipping payment creation.")
            payments = db.session.query(Payment).all()
        else:
            # Add payments
            payments = add_payments(users, subjects)

        print("Initial dummy data added successfully!")

def add_users():
    """Add dummy users with realistic Indian names"""
    print("Adding users...")

    users = []

    # Admin user
    admin = User(
        mobile_number="9876543210",
        name="Rajesh Kumar",
        avatar="admin_avatar.jpg",
        role="admin",
        created_at=datetime.utcnow() - timedelta(days=90),
        last_login=datetime.utcnow(),
        is_profile_complete=True
    )
    users.append(admin)

    # Student users with realistic Indian names and mobile numbers
    student_data = [
        {"mobile": "9876543211", "name": "Priya Sharma", "avatar": "student1.jpg"},
        {"mobile": "9876543212", "name": "Amit Patel", "avatar": "student2.jpg"},
        {"mobile": "9876543213", "name": "Sneha Gupta", "avatar": "student3.jpg"},
        {"mobile": "9876543214", "name": "Vikram Singh", "avatar": "student4.jpg"},
        {"mobile": "9876543215", "name": "Neha Verma", "avatar": "student5.jpg"},
        {"mobile": "9876543216", "name": "Arjun Reddy", "avatar": "student6.jpg"},
        {"mobile": "9876543217", "name": "Ananya Desai", "avatar": "student7.jpg"},
        {"mobile": "9876543218", "name": "Rahul Mehta", "avatar": "student8.jpg"},
        {"mobile": "9876543219", "name": "Pooja Iyer", "avatar": "student9.jpg"}
    ]

    for data in student_data:
        student = User(
            mobile_number=data["mobile"],
            name=data["name"],
            avatar=data["avatar"],
            role="student",
            created_at=datetime.utcnow() - timedelta(days=random.randint(1, 60)),
            last_login=datetime.utcnow() - timedelta(minutes=random.randint(5, 1440)),
            is_profile_complete=True
        )
        users.append(student)

    db.session.add_all(users)
    db.session.commit()
    print(f"Added {len(users)} users")
    return users

def add_exam_categories_and_subjects():
    """Add exam categories and subjects with realistic Indian exam names"""
    print("Adding exam categories and subjects...")

    categories = [
        ExamCategory(
            name="NEET",
            description="National Eligibility cum Entrance Test for Medical Admissions",
            icon="neet_icon.png"
        ),
        ExamCategory(
            name="JEE",
            description="Joint Entrance Examination for Engineering",
            icon="jee_icon.png"
        ),
        ExamCategory(
            name="GATE",
            description="Graduate Aptitude Test in Engineering",
            icon="gate_icon.png"
        )
    ]

    db.session.add_all(categories)
    db.session.commit()

    subjects = []

    # NEET subjects
    neet_subjects = [
        {"name": "Physics", "description": "NEET Physics - Mechanics, Thermodynamics, etc.", "duration": 60},
        {"name": "Chemistry", "description": "NEET Chemistry - Organic, Inorganic & Physical", "duration": 60},
        {"name": "Biology", "description": "NEET Biology - Zoology & Botany", "duration": 60}
    ]

    for subject_data in neet_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=f"{subject_data['name'].lower()}_icon.png",
            duration_minutes=subject_data["duration"],
            category_id=categories[0].id
        )
        subjects.append(subject)

    # JEE subjects
    jee_subjects = [
        {"name": "Mathematics", "description": "JEE Mathematics - Algebra, Calculus & Geometry", "duration": 60},
        {"name": "Physics", "description": "JEE Physics - Mechanics, Electromagnetism, etc.", "duration": 60},
        {"name": "Chemistry", "description": "JEE Chemistry - Physical, Organic & Inorganic", "duration": 60}
    ]

    for subject_data in jee_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=f"{subject_data['name'].lower()}_icon.png",
            duration_minutes=subject_data["duration"],
            category_id=categories[1].id
        )
        subjects.append(subject)

    # GATE subjects
    gate_subjects = [
        {"name": "Computer Science", "description": "GATE CS - Algorithms, OS, DBMS", "duration": 180},
        {"name": "Electronics", "description": "GATE ECE - Digital, Analog, Communications", "duration": 180}
    ]

    for subject_data in gate_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=f"{subject_data['name'].lower().replace(' ', '_')}_icon.png",
            duration_minutes=subject_data["duration"],
            category_id=categories[2].id
        )
        subjects.append(subject)

    db.session.add_all(subjects)
    db.session.commit()

    print(f"Added {len(categories)} categories and {len(subjects)} subjects")
    return categories, subjects

def add_questions_with_options(subjects):
    """Add realistic questions for each subject"""
    print("Adding questions with options...")

    questions = []

    # Sample questions for Physics
    physics_questions = [
        {
            "text": "A body of mass 2 kg is thrown vertically upward with a velocity of 20 m/s. What is its kinetic energy at the highest point?",
            "options": ["0 J", "200 J", "400 J", "800 J"],
            "correct": "A",
            "explanation": "At the highest point, vertical velocity becomes zero, hence kinetic energy is 0 J."
        },
        {
            "text": "Which of the following is the SI unit of pressure?",
            "options": ["Pascal", "Newton", "Joule", "Watt"],
            "correct": "A",
            "explanation": "Pascal (Pa) is the SI unit of pressure, defined as force per unit area (N/m²)."
        }
    ]

    # Sample questions for Chemistry
    chemistry_questions = [
        {
            "text": "What is the hybridization of carbon in methane (CH₄)?",
            "options": ["sp³", "sp²", "sp", "None of these"],
            "correct": "A",
            "explanation": "In methane, carbon forms four single bonds with hydrogen atoms, exhibiting sp³ hybridization."
        },
        {
            "text": "Which of the following is a strong acid?",
            "options": ["HCl", "CH₃COOH", "H₂CO₃", "NH₄OH"],
            "correct": "A",
            "explanation": "HCl (Hydrochloric acid) is a strong acid as it completely dissociates in water."
        }
    ]

    for subject in subjects:
        question_set = []
        if "Physics" in subject.name:
            question_set = physics_questions
        elif "Chemistry" in subject.name:
            question_set = chemistry_questions

        for q_data in question_set:
            question = QuestionWithOptions(
                subject_id=subject.id,
                text=q_data["text"],
                difficulty=random.choice(["easy", "medium", "hard"]),
                marks=4,
                negative_marks=1,
                option_a=q_data["options"][0],
                option_b=q_data["options"][1],
                option_c=q_data["options"][2],
                option_d=q_data["options"][3],
                correct_option=q_data["correct"],
                explanation=q_data["explanation"],
                created_at=datetime.utcnow() - timedelta(days=random.randint(1, 30))
            )
            questions.append(question)

    db.session.add_all(questions)
    db.session.commit()

    print(f"Added {len(questions)} questions")
    return questions

def add_user_progress(users, subjects, questions):
    """Add user exams, attempts, and answers"""
    print("Adding user progress data...")

    user_exams = []
    exam_attempts = []
    user_answers = []

    # Skip admin user
    for user in users[1:]:
        # Each user purchases 2-3 subjects
        selected_subjects = random.sample(subjects, random.randint(2, 3))

        for subject in selected_subjects:
            # Create user exam (purchase)
            user_exam = UserExam(
                user_id=user.id,
                subject_id=subject.id,
                purchased_at=datetime.utcnow() - timedelta(days=random.randint(1, 30)),
                purchase_count=1,
                max_retakes=3,
                retakes_used=random.randint(0, 2)
            )
            user_exams.append(user_exam)

    # First commit the user_exams to get their IDs
    db.session.add_all(user_exams)
    db.session.commit()

    # Now create attempts with valid user_exam_id values
    for user_exam in user_exams:
        # Add 1-2 attempts for this subject
        num_attempts = random.randint(1, 2)

        # Get questions for this subject
        subject_questions = [q for q in questions if q.subject_id == user_exam.subject_id]

        if not subject_questions:
            continue

        for attempt_num in range(num_attempts):
            # Calculate scores
            total_questions = len(subject_questions)
            correct = random.randint(total_questions // 3, total_questions)
            incorrect = random.randint(0, total_questions - correct)
            unattempted = total_questions - correct - incorrect

            # Calculate score
            score = (correct * 4) - (incorrect * 1)

            # Create attempt
            attempt = ExamAttempt(
                user_id=user_exam.user_id,
                user_exam_id=user_exam.id,  # Now we have a valid ID
                attempt_number=attempt_num + 1,
                score=score,
                total_questions=total_questions,
                correct_answers=correct,
                wrong_answers=incorrect,
                unattempted=unattempted,
                time_taken_seconds=random.randint(1800, 3600),
                started_at=datetime.utcnow() - timedelta(days=random.randint(1, 15)),
                completed_at=datetime.utcnow() - timedelta(days=random.randint(0, 14))
            )
            exam_attempts.append(attempt)

    # Commit the attempts to get their IDs
    db.session.add_all(exam_attempts)
    db.session.commit()

    # Now create answers with valid attempt_id values
    for attempt in exam_attempts:
        # Get the user_exam for this attempt
        user_exam = next(ue for ue in user_exams if ue.id == attempt.user_exam_id)

        # Get questions for this subject
        subject_questions = [q for q in questions if q.subject_id == user_exam.subject_id]

        # Track remaining correct/incorrect answers
        remaining_correct = attempt.correct_answers
        remaining_incorrect = attempt.wrong_answers

        # Add answers for each question
        for question in subject_questions:
            if remaining_correct > 0:
                # Correct answer
                answer = UserAnswer(
                    attempt_id=attempt.id,
                    question_id=question.id,
                    selected_option=question.correct_option,
                    time_spent_seconds=random.randint(60, 180)
                )
                remaining_correct -= 1
            elif remaining_incorrect > 0:
                # Wrong answer
                wrong_options = [opt for opt in ['A', 'B', 'C', 'D'] if opt != question.correct_option]
                answer = UserAnswer(
                    attempt_id=attempt.id,
                    question_id=question.id,
                    selected_option=random.choice(wrong_options),
                    time_spent_seconds=random.randint(60, 180)
                )
                remaining_incorrect -= 1
            else:
                # Unattempted
                continue

            user_answers.append(answer)

    # Commit the answers
    db.session.add_all(user_answers)
    db.session.commit()

    print(f"Added {len(user_exams)} user exams, {len(exam_attempts)} attempts, and {len(user_answers)} answers")
    return user_exams, exam_attempts, user_answers

def add_payments(users, subjects):
    """Add payment records"""
    print("Adding payments...")

    payments = []

    # Skip admin user
    for user in users[1:]:
        # Each user has 1-2 payments
        num_payments = random.randint(1, 2)

        for _ in range(num_payments):
            # Random payment amount between 499 and 2499
            amount = Decimal(str(random.randint(499, 2499)))

            payment = Payment(
                user_id=user.id,
                amount=amount,
                currency="INR",
                payment_method=random.choice(["upi", "credit_card", "debit_card"]),
                transaction_id=f"TXN{random.randint(100000, 999999)}",
                status=random.choice(["completed", "completed", "completed", "failed"]),
                payment_date=datetime.utcnow() - timedelta(days=random.randint(1, 30)),
                created_at=datetime.utcnow() - timedelta(days=random.randint(1, 30))
            )
            payments.append(payment)

    db.session.add_all(payments)
    db.session.commit()

    print(f"Added {len(payments)} payments")
    return payments

if __name__ == "__main__":
    add_initial_dummy_data()
