from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.api import bp
from app.models.exam import Question
from app.models.payment import Purchase
from app.models.user_progress import ExamAttempt
from datetime import datetime, timezone

@bp.route('/user/exams', methods=['GET'])
def get_user_exams():
    """Get all exams purchased by the current user"""
    try:
        # For development, make this endpoint accessible without JWT
        # In a real app, you would use @jwt_required() and get_jwt_identity()

        # Try to get the JWT identity if available
        try:
            from flask_jwt_extended import get_jwt_identity
            current_user_id = get_jwt_identity()
            print(f"JWT identity found: {current_user_id}")
        except Exception as jwt_error:
            print(f"JWT error: {str(jwt_error)}")
            # For development, use a default user ID
            current_user_id = 1
            print(f"Using default user ID: {current_user_id}")

        if not current_user_id:
            return jsonify({'error': 'Invalid user identity'}), 401

        # For development, if no purchases exist, return an empty array
        purchases = Purchase.query.filter_by(user_id=current_user_id).all()

        result = []
        for purchase in purchases:
            try:
                # Skip purchases with missing subjects
                if not purchase.subject:
                    continue

                result.append({
                    'id': purchase.id,
                    'user_id': purchase.user_id,
                    'subject_id': purchase.subject_id,
                    'subject': {
                        'id': purchase.subject.id,
                        'name': purchase.subject.subject_name,
                        'description': purchase.subject.description,
                        'category_id': purchase.subject.exam_category_id,
                        'category_name': purchase.subject.category.name if purchase.subject.category else None
                    },
                    'purchased_at': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
                    'max_retakes': purchase.max_retakes,
                    'retakes_used': purchase.retake_used,
                    'retakes_remaining': purchase.max_retakes - purchase.retake_used,
                    'marks': purchase.marks,
                    'negative_marks': purchase.negative_marks,
                    'attempts': [attempt.to_dict() for attempt in ExamAttempt.query.filter_by(purchase_id=purchase.id).all()],
                    'attempt_count': ExamAttempt.query.filter_by(purchase_id=purchase.id).count()
                })
            except Exception as item_error:
                print(f"Error processing purchase {purchase.id}: {str(item_error)}")
                # Skip this purchase and continue with others
                continue

        return jsonify(result)
    except Exception as e:
        print(f"Error in get_user_exams: {str(e)}")
        return jsonify({'error': 'An error occurred while fetching user exams', 'details': str(e)}), 500

@bp.route('/user/exams/<int:purchase_id>/start', methods=['POST'])
@jwt_required()
def start_exam_attempt(purchase_id):
    """Start a new exam attempt (limited to max_retakes per purchase)"""
    current_user_id = get_jwt_identity()

    # Check if purchase exists and belongs to the current user
    purchase = Purchase.query.get_or_404(purchase_id)

    if purchase.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this exam'}), 403

    # Check if user has retakes remaining
    if purchase.retake_used >= purchase.max_retakes:
        return jsonify({
            'error': 'Maximum retakes limit reached for this exam',
            'retakes_used': purchase.retake_used,
            'max_retakes': purchase.max_retakes
        }), 400

    # Get questions for this subject
    questions = Question.query.filter_by(subject_id=purchase.subject_id).all()
    total_questions = len(questions)

    if total_questions == 0:
        return jsonify({'error': 'No questions available for this exam'}), 400

    # Calculate attempt number (previous attempts count + 1)
    attempt_number = ExamAttempt.query.filter_by(purchase_id=purchase_id).count() + 1

    # Increment retakes used
    purchase.retake_used += 1

    # Create new exam attempt
    attempt = ExamAttempt(
        user_id=current_user_id,
        purchase_id=purchase_id,
        attempt_number=attempt_number,
        total_questions=total_questions,
        started_at=datetime.now(timezone.utc)
    )

    db.session.add(attempt)
    db.session.commit()

    # Return attempt details and questions
    return jsonify({
        'attempt': attempt.to_dict(),
        'questions': [q.to_dict() for q in questions]
    })

@bp.route('/user/attempts/<int:attempt_id>/submit', methods=['POST'])
@jwt_required()
def submit_exam_attempt(attempt_id):
    """Submit an exam attempt with answers"""
    current_user_id = get_jwt_identity()

    # Check if attempt exists and belongs to the current user
    attempt = ExamAttempt.query.get_or_404(attempt_id)

    if attempt.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this attempt'}), 403

    if attempt.completed_at:
        return jsonify({'error': 'This attempt has already been submitted'}), 400

    data = request.get_json() or {}

    if 'answers' not in data:
        return jsonify({'error': 'Answers are required'}), 400

    answers = data['answers']  # Format: {question_id: option_id}
    time_taken = data.get('time_taken_seconds', 0)

    # Get the purchase to determine marks and negative marks
    purchase = Purchase.query.get(attempt.purchase_id)
    marks_per_question = purchase.marks if purchase else 4
    negative_marks = purchase.negative_marks if purchase else 1

    # Calculate score
    correct_answers = 0
    wrong_answers = 0

    for question_id, option_id in answers.items():
        # Get the question and check if the option is correct
        question = Question.query.get(int(question_id))
        if not question:
            continue

        option = next((o for o in question.options if o.id == int(option_id)), None)
        if not option:
            continue

        if option.is_correct:
            correct_answers += 1
        else:
            wrong_answers += 1

    # Calculate score using the marks from the purchase
    score = (correct_answers * marks_per_question) - (wrong_answers * negative_marks)
    unattempted = attempt.total_questions - (correct_answers + wrong_answers)

    # Update attempt
    attempt.score = score
    attempt.correct_answers = correct_answers
    attempt.wrong_answers = wrong_answers
    attempt.unattempted = unattempted
    attempt.time_taken_seconds = time_taken
    attempt.completed_at = datetime.now(timezone.utc)

    db.session.commit()

    return jsonify(attempt.to_dict())

@bp.route('/user/attempts', methods=['GET'])
@jwt_required()
def get_user_attempts():
    """Get all exam attempts by the current user"""
    current_user_id = get_jwt_identity()

    attempts = ExamAttempt.query.filter_by(user_id=current_user_id).all()
    return jsonify([attempt.to_dict() for attempt in attempts])

@bp.route('/user/attempts/<int:attempt_id>', methods=['GET'])
@jwt_required()
def get_attempt_details(attempt_id):
    """Get details of a specific exam attempt"""
    current_user_id = get_jwt_identity()

    attempt = ExamAttempt.query.get_or_404(attempt_id)

    if attempt.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this attempt'}), 403

    return jsonify(attempt.to_dict())

@bp.route('/user/purchases/<int:purchase_id>/attempts', methods=['GET'])
@jwt_required()
def get_purchase_attempts(purchase_id):
    """Get all attempts for a specific purchase"""
    current_user_id = get_jwt_identity()

    # Check if purchase exists and belongs to the current user
    purchase = Purchase.query.get_or_404(purchase_id)

    if purchase.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this purchase'}), 403

    attempts = ExamAttempt.query.filter_by(purchase_id=purchase_id).all()
    return jsonify([attempt.to_dict() for attempt in attempts])
