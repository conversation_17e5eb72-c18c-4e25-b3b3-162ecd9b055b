import requests
import json

def test_get_comments():
    # Use a post ID that exists in your database
    post_id = 1
    url = f'http://localhost:5000/api/community/posts/{post_id}/comments'
    
    print(f"Sending GET request to {url}")
    
    try:
        response = requests.get(url)
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            comments = response.json()
            print(f"Number of comments returned: {len(comments)}")
            if comments:
                print("First comment:")
                print(f"  ID: {comments[0].get('id')}")
                print(f"  Content: {comments[0].get('content')[:50]}...")
                print(f"  User: {comments[0].get('user_name')}")
                print(f"  Likes: {comments[0].get('likes_count')}")
            else:
                print("No comments returned.")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def test_add_comment():
    # Use a post ID that exists in your database
    post_id = 1
    url = f'http://localhost:5000/api/community/posts/{post_id}/comments'
    data = {
        'content': 'This is a test comment from Python script'
    }
    
    print(f"Sending POST request to {url} with data: {data}")
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        if response.status_code == 200 or response.status_code == 201:
            print(f"Response: {response.json()}")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    print("Testing comments API endpoints...")
    test_get_comments()
    test_add_comment()
    test_get_comments()  # Get comments again to see the new comment
    print("Testing complete!")
