from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.ai import ai_bp
from app.models.exam import Subject
import os
import shutil
import json
from werkzeug.utils import secure_filename

# Try to import ollama
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    print("Warning: ollama not available. MCQ generation will not work.")
    OLLAMA_AVAILABLE = False

# Try to import PyPDF2
try:
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    print("Warning: PyPDF2 not available. PDF processing will not work.")
    PYPDF2_AVAILABLE = False

# Try to import sentence transformers
try:
    from sentence_transformers import SentenceTransformer
    embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: sentence_transformers not available. Some features will be limited.")
    embedding_model = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# # Create a directory for storing uploaded PDFs
# UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
# if not os.path.exists(UPLOAD_FOLDER):
#     os.makedirs(UPLOAD_FOLDER)

# @ai_bp.route('/process-pdf', methods=['POST'])
# def process_pdf():
#     """Process a single PDF file and create embeddings"""
#     if 'file' not in request.files:
#         return jsonify({'error': 'No file provided'}), 400

#     file = request.files['file']
#     if file.filename == '':
#         return jsonify({'error': 'No file selected'}), 400

#     if not file.filename.endswith('.pdf'):
#         return jsonify({'error': 'File must be a PDF'}), 400

#     # Save the file temporarily
#     temp_path = f"temp_{secure_filename(file.filename)}"
#     file.save(temp_path)

#     try:
#         num_chunks = ai_service.process_pdf(temp_path)
#         return jsonify({
#             'message': 'PDF processed successfully',
#             'chunks': num_chunks
#         })
#     finally:
#         # Clean up temporary file
#         if os.path.exists(temp_path):
#             os.remove(temp_path)

# @ai_bp.route('/process-pdfs', methods=['POST'])
# def process_pdfs():
#     """Process multiple PDF files and create embeddings"""
#     if 'files[]' not in request.files:
#         return jsonify({'error': 'No files provided'}), 400

#     files = request.files.getlist('files[]')
#     if not files or len(files) == 0:
#         return jsonify({'error': 'No files selected'}), 400

#     # Create a temporary directory for the uploaded files
#     temp_dir = os.path.join(UPLOAD_FOLDER, 'temp_batch')
#     if os.path.exists(temp_dir):
#         shutil.rmtree(temp_dir)
#     os.makedirs(temp_dir)

#     try:
#         # Save all files to the temporary directory
#         for file in files:
#             if file.filename == '':
#                 continue

#             if not file.filename.endswith('.pdf'):
#                 continue

#             file_path = os.path.join(temp_dir, secure_filename(file.filename))
#             file.save(file_path)

#         # Process all PDFs in the directory
#         num_chunks = ai_service.process_pdf_directory(temp_dir)

#         return jsonify({
#             'message': f'Successfully processed {len(files)} PDF files',
#             'chunks': num_chunks
#         })
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500
#     finally:
#         # Clean up temporary directory
#         if os.path.exists(temp_dir):
#             shutil.rmtree(temp_dir)

@ai_bp.route('/chat', methods=['POST'])
def chat():
    """Get a response from the chatbot using Ollama"""
    data = request.get_json() or {}

    if 'question' not in data:
        return jsonify({'error': 'Question is required'}), 400

    if not OLLAMA_AVAILABLE:
        return jsonify({
            'error': 'Ollama is not available. Please install the ollama package.'
        }), 500

    try:
        # Get chat history from request
        chat_history = data.get('chat_history', [])
        question = data['question']

        # Get user ID if available
        user_id = None
        if request.headers.get('Authorization'):
            try:
                user_id = get_jwt_identity()
            except:
                # Continue without user ID
                pass

        # Generate response using Ollama
        response_text = generate_chat_response_ollama(question, chat_history)

        # Create response object
        response_data = {
            'response': response_text,
            'timestamp': data.get('timestamp', None),
            'model': 'llama3.2:1b'
        }

        return jsonify(response_data)
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

# @ai_bp.route('/generate-mcq', methods=['POST'])
# @jwt_required()
# def generate_mcq():
#     """Generate MCQ questions for a given topic and store them"""
#     data = request.get_json() or {}

#     if not all(k in data for k in ('topic', 'subject_id')):
#         return jsonify({'error': 'Topic and subject_id are required'}), 400

#     try:
#         user_id = get_jwt_identity()
#         num_questions = data.get('num_questions', 5)

#         # Check if subject exists
#         subject = Subject.query.get(data['subject_id'])
#         if not subject:
#             return jsonify({'error': 'Invalid subject ID'}), 404

#         # Generate and store questions
#         questions = ai_service.generate_and_store_mcq(
#             topic=data['topic'],
#             subject_id=data['subject_id'],
#             user_id=user_id,
#             num_questions=num_questions
#         )

#         return jsonify({
#             'message': f'Successfully generated {len(questions)} questions',
#             'questions': [q.to_dict() for q in questions]
#         })

#     except ValueError as e:
#         return jsonify({'error': str(e)}), 400
#     except Exception as e:
#         print(f"Error generating MCQ questions: {e}")
#         return jsonify({'error': f'Failed to generate questions: {str(e)}'}), 500

# @ai_bp.route('/generate-mcq-preview', methods=['POST'])
# def generate_mcq_preview():
#     """Generate MCQ questions for preview without storing them"""
#     data = request.get_json() or {}

#     if 'topic' not in data:
#         return jsonify({'error': 'Topic is required'}), 400

#     try:
#         num_questions = data.get('num_questions', 3)
#         subject_name = data.get('subject_name', 'General')

#         # Generate MCQs without storing them
#         if ai_service.vector_store:
#             mcq_json = ai_service.generate_mcq(data['topic'], num_questions)
#         else:
#             mcq_json = ai_service.generate_general_mcq(data['topic'], subject_name, num_questions)

#         # Parse the response as JSON
#         questions_data = json.loads(mcq_json)

#         return jsonify({
#             'message': f'Successfully generated {len(questions_data)} questions',
#             'questions': questions_data
#         })
#     except json.JSONDecodeError as e:
#         return jsonify({'error': f'Failed to parse AI response as JSON: {str(e)}'}), 400
#     except Exception as e:
#         print(f"Error generating MCQ preview: {e}")
#         return jsonify({'error': f'Failed to generate questions: {str(e)}'}), 500

# PDF folder configuration
PDF_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'pdfs')

# Global variables for storing processed data
texts = []
sources = []
embeddings = []

# Helper functions for PDF processing and MCQ generation
def extract_texts_from_pdfs(folder_path):
    """Extract text from all PDF files in the given folder"""
    if not PYPDF2_AVAILABLE:
        print("Cannot extract text from PDFs: PyPDF2 not available")
        return []

    # Check if the folder exists
    if not os.path.exists(folder_path):
        print(f"Warning: PDF folder '{folder_path}' does not exist. Creating it.")
        os.makedirs(folder_path, exist_ok=True)
        return []

    all_text = []
    for filename in os.listdir(folder_path):
        if filename.endswith('.pdf'):
            pdf_path = os.path.join(folder_path, filename)
            try:
                reader = PdfReader(pdf_path)
                text = ''
                for page in reader.pages:
                    text += page.extract_text() or ''
                all_text.append((text, filename))
            except Exception as e:
                print(f"Error reading PDF {filename}: {e}")
                continue
    return all_text

def create_embeddings(text_data):
    """Create embeddings for text data"""
    texts = [item[0] for item in text_data]
    sources = [item[1] for item in text_data]

    if not SENTENCE_TRANSFORMERS_AVAILABLE:
        print("Cannot create embeddings: sentence_transformers not available")
        return None, texts, sources

    try:
        text_embeddings = embedding_model.encode(texts, convert_to_tensor=False)
        return text_embeddings, texts, sources
    except Exception as e:
        print(f"Error creating embeddings: {e}")
        return None, texts, sources

def load_or_create_index():
    """Load or create embeddings from PDF files"""
    data = extract_texts_from_pdfs(PDF_FOLDER)
    if not data:
        return None, [], []
    text_embeddings, texts, sources = create_embeddings(data)
    return text_embeddings, texts, sources

def generate_mcq_from_combined_content(all_texts, sources, max_content_length=8000, num_questions=5, topic=None, subject=None):
    """Generate MCQ using Ollama from combined content"""
    if not OLLAMA_AVAILABLE:
        print("Cannot generate MCQs: ollama not available")
        return "MCQ generation not available. Please install ollama package.", []

    # Combine content from multiple PDFs, keeping track of sources
    combined_content = ""
    used_sources = []

    for i, (text, source) in enumerate(zip(all_texts, sources)):
        if len(combined_content) + len(text[:1000]) < max_content_length:
            combined_content += f"\n--- From {source} ---\n{text[:1000]}\n"
            used_sources.append(source)
        else:
            break

    # Create a more specific prompt based on topic and subject
    topic_context = f" about {topic}" if topic else ""
    subject_context = f" for {subject} subject" if subject else ""

    prompt = f"""Based on the following content from multiple educational documents, generate {num_questions} comprehensive multiple-choice questions{topic_context}{subject_context}.

Make sure the questions cover different topics and difficulty levels from the provided content.

Content from multiple sources:
\"\"\"
{combined_content}
\"\"\"

Format the response as a valid JSON array where each question object has these exact fields:
[
    {{
        "question": "Question text here",
        "options": ["A. Option A text", "B. Option B text", "C. Option C text", "D. Option D text"],
        "correct_answer": "A",
        "explanation": "Detailed explanation of why this answer is correct",
        "difficulty": "medium"
    }},
    ...
]

Requirements:
- Each question must have exactly 4 options labeled A, B, C, D
- The correct_answer must be one of: A, B, C, or D
- Difficulty must be one of: easy, medium, hard
- Ensure the JSON is properly formatted and valid
- Do not include any text before or after the JSON array

Sources used: {', '.join(used_sources)}
"""

    try:
        response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
        return response['message']['content'], used_sources
    except Exception as e:
        print(f"Error generating MCQ with Ollama: {e}")
        return f"Error generating MCQ: {str(e)}", used_sources

def generate_chat_response_ollama(question, chat_history=[]):
    """Generate a chat response using Ollama for educational assistance"""
    if not OLLAMA_AVAILABLE:
        return "I'm sorry, but the AI service is currently unavailable. Please try again later."

    # Format chat history for context
    context = ""
    if chat_history:
        for entry in chat_history[-5:]:  # Use last 5 messages for context
            role = entry.get('role', 'user')
            content = entry.get('content', '')
            if role == 'user':
                context += f"Student: {content}\n"
            else:
                context += f"AI Master: {content}\n"

    # Create a comprehensive prompt for educational assistance
    prompt = f"""You are an AI Master, an expert educational assistant specializing in exam preparation for competitive exams like NEET, JEE, and other academic subjects. You are helpful, knowledgeable, and encouraging.

Previous conversation:
{context}

Current question: {question}

Please provide a helpful, accurate, and educational response. If the question is about:
- Study techniques: Provide practical study methods and tips
- Subject concepts: Explain clearly with examples when possible
- Exam preparation: Give specific strategies and advice
- Practice questions: Help solve or explain the approach
- Motivation: Provide encouraging and supportive guidance

Keep your response conversational, helpful, and focused on education. If you're not sure about something, it's okay to say so and suggest where the student might find more information.

Response:"""

    try:
        response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
        return response['message']['content']
    except Exception as e:
        print(f"Error generating chat response with Ollama: {e}")
        return "I'm sorry, I encountered an error while processing your question. Please try asking again or rephrase your question."

def generate_general_mcq_ollama(topic, subject=None, num_questions=5):
    """Generate MCQ using Ollama without PDF content (general knowledge)"""
    if not OLLAMA_AVAILABLE:
        return "MCQ generation not available. Please install ollama package."

    subject_context = f" for {subject} subject" if subject else ""

    prompt = f"""Generate {num_questions} multiple-choice questions about {topic}{subject_context}.

Format the response as a valid JSON array where each question object has these exact fields:
[
    {{
        "question": "Question text here",
        "options": ["A. Option A text", "B. Option B text", "C. Option C text", "D. Option D text"],
        "correct_answer": "A",
        "explanation": "Detailed explanation of why this answer is correct",
        "difficulty": "medium"
    }},
    ...
]

Requirements:
- Each question must have exactly 4 options labeled A, B, C, D
- The correct_answer must be one of: A, B, C, or D
- Difficulty must be one of: easy, medium, hard
- Questions should be educational and appropriate for exam preparation
- Ensure the JSON is properly formatted and valid
- Do not include any text before or after the JSON array
"""

    try:
        response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
        return response['message']['content']
    except Exception as e:
        print(f"Error generating general MCQ with Ollama: {e}")
        return f"Error generating MCQ: {str(e)}"

# API Endpoints
@ai_bp.route('/generate-mcq-ollama', methods=['POST'])
def generate_mcq_ollama():
    """Generate MCQ questions using Ollama with optional PDF content"""
    global texts, sources, embeddings

    if not OLLAMA_AVAILABLE:
        return jsonify({
            'status': 'error',
            'message': 'Ollama is not available. Please install the ollama package.'
        }), 500

    data = request.get_json() or {}

    # Get parameters from request
    topic = data.get('topic', 'general knowledge')
    subject = data.get('subject', None)
    num_questions = data.get('num_questions', 5)
    use_pdf_content = data.get('use_pdf_content', True)

    # Validate num_questions
    if not isinstance(num_questions, int) or num_questions < 1 or num_questions > 20:
        return jsonify({
            'status': 'error',
            'message': 'num_questions must be an integer between 1 and 20'
        }), 400

    try:
        if use_pdf_content:
            # Try to use PDF content if available
            embeddings, texts, sources = load_or_create_index()

            if texts:
                # Generate MCQs from PDF content
                mcq_content, used_sources = generate_mcq_from_combined_content(
                    texts, sources, num_questions=num_questions, topic=topic, subject=subject
                )

                # Try to parse as JSON to validate
                try:
                    mcq_json = json.loads(mcq_content)
                    result = {
                        'status': 'success',
                        'questions': mcq_json,
                        'sources_used': used_sources,
                        'total_pdfs_processed': len(texts),
                        'generation_method': 'pdf_content'
                    }
                except json.JSONDecodeError:
                    # If JSON parsing fails, return raw content with warning
                    result = {
                        'status': 'partial_success',
                        'raw_content': mcq_content,
                        'sources_used': used_sources,
                        'total_pdfs_processed': len(texts),
                        'generation_method': 'pdf_content',
                        'warning': 'Generated content may not be in valid JSON format'
                    }
            else:
                # No PDF content available, use general knowledge
                mcq_content = generate_general_mcq_ollama(topic, subject, num_questions)

                try:
                    mcq_json = json.loads(mcq_content)
                    result = {
                        'status': 'success',
                        'questions': mcq_json,
                        'generation_method': 'general_knowledge',
                        'message': 'No PDF content available, generated from general knowledge'
                    }
                except json.JSONDecodeError:
                    result = {
                        'status': 'partial_success',
                        'raw_content': mcq_content,
                        'generation_method': 'general_knowledge',
                        'warning': 'Generated content may not be in valid JSON format'
                    }
        else:
            # Generate from general knowledge only
            mcq_content = generate_general_mcq_ollama(topic, subject, num_questions)

            try:
                mcq_json = json.loads(mcq_content)
                result = {
                    'status': 'success',
                    'questions': mcq_json,
                    'generation_method': 'general_knowledge'
                }
            except json.JSONDecodeError:
                result = {
                    'status': 'partial_success',
                    'raw_content': mcq_content,
                    'generation_method': 'general_knowledge',
                    'warning': 'Generated content may not be in valid JSON format'
                }

    except Exception as e:
        result = {
            'status': 'error',
            'message': f'Error generating MCQs: {str(e)}'
        }

    return jsonify(result)

@ai_bp.route('/generate-mcq-from-pdfs', methods=['GET', 'POST'])
def generate_mcq_from_pdfs():
    """Generate MCQ questions from PDF files in the pdfs directory using Ollama"""
    global texts, sources, embeddings

    if not OLLAMA_AVAILABLE:
        return jsonify({
            'status': 'error',
            'message': 'Ollama is not available. Please install the ollama package.'
        }), 500

    # Get parameters from request (for POST) or use defaults (for GET)
    if request.method == 'POST':
        data = request.get_json() or {}
        num_questions = data.get('num_questions', 5)
        topic = data.get('topic', None)
        subject = data.get('subject', None)
    else:
        num_questions = int(request.args.get('num_questions', 5))
        topic = request.args.get('topic', None)
        subject = request.args.get('subject', None)

    # Validate num_questions
    if not isinstance(num_questions, int) or num_questions < 1 or num_questions > 20:
        return jsonify({
            'status': 'error',
            'message': 'num_questions must be an integer between 1 and 20'
        }), 400

    embeddings, texts, sources = load_or_create_index()

    if not texts:
        return jsonify({
            'status': 'error',
            'message': f'No PDF files found in the pdfs directory: {PDF_FOLDER}'
        })

    try:
        # Generate MCQs from combined content of all PDFs
        mcq_content, used_sources = generate_mcq_from_combined_content(
            texts, sources, num_questions=num_questions, topic=topic, subject=subject
        )

        # Try to parse as JSON
        try:
            mcq_json = json.loads(mcq_content)
            result = {
                'status': 'success',
                'questions': mcq_json,
                'sources_used': used_sources,
                'total_pdfs_processed': len(texts),
                'total_sources_used_for_mcq': len(used_sources),
                'pdf_folder': PDF_FOLDER
            }
        except json.JSONDecodeError:
            # If JSON parsing fails, return raw content with warning
            result = {
                'status': 'partial_success',
                'raw_content': mcq_content,
                'sources_used': used_sources,
                'total_pdfs_processed': len(texts),
                'total_sources_used_for_mcq': len(used_sources),
                'pdf_folder': PDF_FOLDER,
                'warning': 'Generated content may not be in valid JSON format'
            }

    except Exception as e:
        result = {
            'status': 'error',
            'message': f'Error generating MCQs: {str(e)}',
            'total_pdfs_processed': len(texts),
            'pdf_folder': PDF_FOLDER
        }

    return jsonify(result)

@ai_bp.route('/status', methods=['GET'])
def ai_status():
    """Check the status of AI services and dependencies"""
    status = {
        'status': 'running',
        'dependencies': {
            'ollama': OLLAMA_AVAILABLE,
            'PyPDF2': PYPDF2_AVAILABLE,
            'sentence_transformers': SENTENCE_TRANSFORMERS_AVAILABLE
        },
        'pdf_folder': PDF_FOLDER,
        'pdf_folder_exists': os.path.exists(PDF_FOLDER)
    }

    # Check if PDF folder has any PDF files
    if os.path.exists(PDF_FOLDER):
        pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.endswith('.pdf')]
        status['pdf_files_count'] = len(pdf_files)
        status['pdf_files'] = pdf_files[:10]  # Show first 10 files
    else:
        status['pdf_files_count'] = 0
        status['pdf_files'] = []

    return jsonify(status)
