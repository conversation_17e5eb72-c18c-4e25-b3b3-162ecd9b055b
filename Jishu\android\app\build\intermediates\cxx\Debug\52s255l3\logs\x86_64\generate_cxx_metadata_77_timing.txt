# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 20ms
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 15ms
  [gap of 16ms]
generate_cxx_metadata completed in 63ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
generate_cxx_metadata completed in 20ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 17ms
generate_cxx_metadata completed in 40ms

