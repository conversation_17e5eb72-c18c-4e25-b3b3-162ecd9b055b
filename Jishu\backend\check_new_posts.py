from app import create_app
from app.models.community import CommunityPost

app = create_app()
with app.app_context():
    # Get the latest post
    latest_post = CommunityPost.query.order_by(CommunityPost.id.desc()).first()
    
    if latest_post:
        print(f"Latest post:")
        print(f"ID: {latest_post.id}")
        print(f"User ID: {latest_post.user_id}")
        print(f"Content: {latest_post.content}")
        print(f"Title: {latest_post.title}")
        print(f"Likes: {latest_post.likes_count}")
        print(f"Comments: {latest_post.comments_count}")
        print(f"Created at: {latest_post.created_at}")
    else:
        print("No posts found in the database.")
