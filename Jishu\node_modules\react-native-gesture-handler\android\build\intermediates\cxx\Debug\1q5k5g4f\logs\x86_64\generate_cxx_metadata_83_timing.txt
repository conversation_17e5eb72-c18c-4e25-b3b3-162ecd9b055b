# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
generate_cxx_metadata completed in 23ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 20ms
  [gap of 16ms]
generate_cxx_metadata completed in 47ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 12ms
generate_cxx_metadata completed in 15ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
generate_cxx_metadata completed in 23ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 43ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
  [gap of 17ms]
generate_cxx_metadata completed in 37ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 24ms
generate_cxx_metadata completed in 45ms

