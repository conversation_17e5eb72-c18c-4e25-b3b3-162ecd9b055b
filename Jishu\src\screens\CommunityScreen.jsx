import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
  Image,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useSelector } from 'react-redux';
import { communityAPI, chatAPI } from '../services/api';
import { useNavigation } from '@react-navigation/native';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336',
  lightGray: '#D1D1D1'
};

const CommunityScreen = () => {
  const navigation = useNavigation();
  const { user } = useSelector((state) => state.auth);
  const userAvatar = user?.avatar || 'https://randomuser.me/api/portraits/men/32.jpg';

  // Tab state
  const [activeTab, setActiveTab] = useState('communities');

  // State for posts and pagination
  const [posts, setPosts] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loading, setLoading] = useState(true);

  // State for comments and replies
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [postComments, setPostComments] = useState({});

  // State for inbox/chats
  const [conversations, setConversations] = useState([]);
  const [loadingChats, setLoadingChats] = useState(false);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'communities') {
      loadPosts();
    } else if (activeTab === 'inbox') {
      loadConversations();
    }
  }, [activeTab]);

  // Function to load conversations
  const loadConversations = async () => {
    try {
      setLoadingChats(true);

      // Use the chatAPI to fetch personal chats
      const response = await chatAPI.getPersonalChats();

      // Process the response
      if (Array.isArray(response)) {
        // If the response is an empty array, set empty conversations
        if (response.length === 0) {
          setConversations([]);
          return;
        }

        // Convert string timestamps to Date objects for proper formatting
        const processedConversations = response.map(conversation => ({
          ...conversation,
          timestamp: new Date(conversation.timestamp)
        }));

        setConversations(processedConversations);
      } else {
        console.error('Invalid response format from chat API:', response);
        setConversations([]);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);

      // Check if we're in development mode
      if (__DEV__) {
        // Fallback to mock data if API fails in development mode
        const mockConversations = [
          {
            id: '1',
            user_id: '101',
            user_name: 'Krish C Naik',
            user_avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            last_message: 'hello sir i want talk to you, i joined your cours...',
            unread_count: 1,
            timestamp: new Date('2025-03-31T11:45:00'),
            is_creator: true
          },
          {
            id: '2',
            user_id: '102',
            user_name: 'Rahul Sharma',
            user_avatar: 'https://randomuser.me/api/portraits/men/44.jpg',
            last_message: 'Thanks for your help with the practice questions!',
            unread_count: 0,
            timestamp: new Date('2025-03-30T15:30:00'),
            is_creator: false
          }
        ];

        setConversations(mockConversations);
      } else {
        // In production, just set empty conversations
        setConversations([]);
      }
    } finally {
      setLoadingChats(false);
    }
  };

  // Function to load posts with pagination
  const loadPosts = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
        setPage(1);
      } else if (!refresh && !loadingMore) {
        setLoading(true);
      }

      const response = await communityAPI.getPosts(refresh ? 1 : page);

      if (response && response.posts) {
        if (refresh || page === 1) {
          setPosts(response.posts);
        } else {
          setPosts(prevPosts => [...prevPosts, ...response.posts]);
        }

        setTotalPages(response.pages || 1);
      }
    } catch (error) {
      console.error('Error loading posts:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Function to handle refresh
  const handleRefresh = useCallback(() => {
    loadPosts(true);
  }, []);

  // Function to load more posts
  const handleLoadMore = useCallback(() => {
    if (page < totalPages && !loadingMore) {
      setLoadingMore(true);
      setPage(prevPage => prevPage + 1);
      loadPosts(false);
    }
  }, [page, totalPages, loadingMore]);

  // Function to load comments for a post
  const loadComments = async (postId) => {
    try {
      console.log(`Loading comments for post ${postId}`);
      // Always reload comments to get the latest
      const comments = await communityAPI.getPostComments(postId);
      console.log(`Received ${comments.length || 0} comments for post ${postId}`);

      setPostComments(prev => ({
        ...prev,
        [postId]: comments
      }));
    } catch (error) {
      console.error(`Error loading comments for post ${postId}:`, error);
      // Set empty array for comments if there's an error
      setPostComments(prev => ({
        ...prev,
        [postId]: []
      }));
    }
  };

  // Format timestamp for posts and comments
  const formatPostTimestamp = (timestamp) => {
    if (!timestamp) return 'Just now';

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return diffDay === 1 ? '1 day ago' : `${diffDay} days ago`;
    } else if (diffHour > 0) {
      return diffHour === 1 ? '1 hour ago' : `${diffHour} hours ago`;
    } else if (diffMin > 0) {
      return diffMin === 1 ? '1 minute ago' : `${diffMin} minutes ago`;
    } else {
      return 'Just now';
    }
  };

  // Handle posting a new post
  const handlePostComment = async () => {
    if (newComment.trim()) {
      try {
        console.log('Attempting to create post with content:', newComment);
        // Pass the user data to the API
        const userData = {
          id: user?.id,
          name: user?.name || 'Guest',
          avatar: userAvatar
        };
        const createdPost = await communityAPI.createPost(newComment, '', userData);
        console.log('Post created successfully:', createdPost);
        setPosts(prevPosts => [createdPost, ...prevPosts]);
        setNewComment('');
        Keyboard.dismiss();

        // Check if the post was added to the database
        setTimeout(async () => {
          try {
            console.log('Refreshing posts to verify post was added...');
            await loadPosts(true);
          } catch (refreshError) {
            console.error('Error refreshing posts:', refreshError);
          }
        }, 1000);
      } catch (error) {
        console.error('Error creating post:', error);
      }
    }
  };

  // Handle replying to a post
  const handleReply = async (postId) => {
    if (replyText.trim()) {
      try {
        console.log(`Attempting to add comment to post ${postId} with content:`, replyText);
        // Pass the user data to the API
        const userData = {
          id: user?.id,
          name: user?.name || 'Guest',
          avatar: userAvatar
        };
        const newComment = await communityAPI.addComment(postId, replyText, userData);
        console.log(`Comment added successfully to post ${postId}:`, newComment);

        // Update local comments state
        setPostComments(prev => {
          const currentComments = prev[postId] || [];
          return {
            ...prev,
            [postId]: [newComment, ...currentComments]
          };
        });

        // Update post comment count
        setPosts(prevPosts =>
          prevPosts.map(post =>
            post.id === postId
              ? { ...post, comments_count: post.comments_count + 1 }
              : post
          )
        );

        setReplyText('');
        // Don't close the comments section after replying
        // setReplyingTo(null);
        Keyboard.dismiss();

        // Refresh comments after a short delay to ensure the new comment is included
        setTimeout(() => {
          loadComments(postId);
        }, 500);
      } catch (error) {
        console.error(`Error adding comment to post ${postId}:`, error);
      }
    }
  };

  // Handle liking a post
  const handleLikePost = async (postId) => {
    try {
      const response = await communityAPI.likePost(postId);

      // Update post likes count
      setPosts(prevPosts =>
        prevPosts.map(post =>
          post.id === postId
            ? { ...post, likes_count: response.likes_count }
            : post
        )
      );
    } catch (error) {
      console.error(`Error liking post ${postId}:`, error);
    }
  };

  // Handle liking a comment
  const handleLikeComment = async (commentId, postId) => {
    try {
      const response = await communityAPI.likeComment(commentId);

      // Update comment likes count
      setPostComments(prev => {
        const currentComments = prev[postId] || [];
        return {
          ...prev,
          [postId]: currentComments.map(comment =>
            comment.id === commentId
              ? { ...comment, likes_count: response.likes_count }
              : comment
          )
        };
      });
    } catch (error) {
      console.error(`Error liking comment ${commentId}:`, error);
    }
  };

  // Toggle showing comments for a post
  const toggleComments = (postId) => {
    console.log(`Toggling comments for post ${postId}, current state:`, replyingTo === postId ? 'open' : 'closed');
    if (replyingTo === postId) {
      // Close comments section
      setReplyingTo(null);
    } else {
      // Open comments section and load comments
      setReplyingTo(postId);
      loadComments(postId);
    }
  };

  // Render a post
  const renderPost = ({ item }) => (
    <View style={styles.postContainer}>
      <View style={styles.postHeader}>
        <Image
          source={{ uri: item.user_avatar }}
          style={styles.avatar}
        />
        <View style={styles.postHeaderText}>
          <Text style={styles.userName}>{item.user_name}</Text>
          <Text style={styles.timestamp}>{formatPostTimestamp(item.created_at)}</Text>
        </View>
      </View>

      <Text style={styles.postText}>{item.content}</Text>

      <View style={styles.postActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleLikePost(item.id)}
          activeOpacity={0.7}
        >
          <MaterialIcons name="thumb-up" size={18} color={COLORS.primary} />
          <Text style={styles.actionText}>{item.likes_count} Likes</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => toggleComments(item.id)}
          activeOpacity={0.7}
        >
          <MaterialIcons name="chat-bubble-outline" size={18} color={COLORS.textSecondary} />
          <Text style={styles.actionText}>{item.comments_count} Comments</Text>
        </TouchableOpacity>
      </View>

      {/* Comments section */}
      {replyingTo === item.id && (
        <View style={styles.commentsSection}>
          {/* Comment input */}
          <View style={styles.replyInputContainer}>
            <Image source={{ uri: userAvatar }} style={styles.replyAvatar} />
            <TextInput
              style={styles.replyInput}
              placeholder="Write a comment..."
              placeholderTextColor={COLORS.textSecondary}
              value={replyText}
              onChangeText={setReplyText}
              multiline
            />
            <TouchableOpacity
              style={[styles.sendButton, !replyText.trim() && styles.sendButtonDisabled]}
              onPress={() => handleReply(item.id)}
              disabled={!replyText.trim()}
            >
              <MaterialIcons
                name="send"
                size={20}
                color={replyText.trim() ? COLORS.primary : COLORS.border}
              />
            </TouchableOpacity>
          </View>

          {/* Comments list */}
          {postComments[item.id] && postComments[item.id].length > 0 ? (
            <View style={styles.commentsList}>
              {postComments[item.id].map(comment => (
                <View key={comment.id} style={styles.commentContainer}>
                  <View style={styles.commentHeader}>
                    <Image
                      source={{ uri: comment.user_avatar }}
                      style={styles.commentAvatar}
                    />
                    <View style={styles.commentInfo}>
                      <Text style={styles.commentUserName}>{comment.user_name}</Text>
                      <Text style={styles.commentTimestamp}>{formatPostTimestamp(comment.created_at)}</Text>
                    </View>
                  </View>

                  <Text style={styles.commentText}>{comment.content}</Text>

                  <TouchableOpacity
                    style={styles.commentLikeButton}
                    onPress={() => handleLikeComment(comment.id, item.id)}
                  >
                    <MaterialIcons name="thumb-up" size={14} color={COLORS.textSecondary} />
                    <Text style={styles.commentLikeText}>{comment.likes_count}</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <Text style={styles.noCommentsText}>No comments yet. Be the first to comment!</Text>
          )}
        </View>
      )}
    </View>
  );

  // Render footer with loading indicator
  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={COLORS.primary} />
        <Text style={styles.loadingMoreText}>Loading more posts...</Text>
      </View>
    );
  };

  // Render empty state for posts
  const renderEmptyPosts = () => {
    if (loading) return null;

    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="forum" size={64} color={COLORS.lightGray || '#D1D1D1'} />
        <Text style={styles.emptyText}>No posts yet</Text>
        <Text style={styles.emptySubText}>Be the first to share something with the community!</Text>
      </View>
    );
  };

  // Render empty state for conversations
  const renderEmptyConversations = () => {
    if (loadingChats) return null;

    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="chat" size={64} color={COLORS.lightGray || '#D1D1D1'} />
        <Text style={styles.emptyText}>No messages yet</Text>
        <Text style={styles.emptySubText}>Your conversations will appear here</Text>
        <TouchableOpacity
          style={styles.emptyButton}
          onPress={() => loadConversations()}
        >
          <Text style={styles.emptyButtonText}>Refresh</Text>
        </TouchableOpacity>

        {/* Add a button to create a new conversation */}
        <TouchableOpacity
          style={[styles.emptyButton, { marginTop: 10, backgroundColor: COLORS.secondary }]}
          onPress={() => {
            // For now, just show a message that this feature is coming soon
            Alert.alert(
              'Coming Soon',
              'The ability to start new conversations will be available in a future update.',
              [{ text: 'OK' }]
            );
          }}
        >
          <Text style={styles.emptyButtonText}>Start a New Chat</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Handle navigation to chat detail
  const handleChatPress = (conversation) => {
    console.log('Navigate to chat with:', conversation.user_name);

    // Extract the user_id from the conversation
    // The API returns user_id as a string, so we need to convert it to a number for the API call
    const userId = parseInt(conversation.user_id, 10);

    try {
      // Navigate to the ChatbotScreen with the necessary parameters
      // Use navigation.getParent() to access the parent navigator (Stack Navigator)
      const rootNavigation = navigation.getParent();
      if (rootNavigation) {
        rootNavigation.navigate('ChatbotScreen', {
          conversationId: conversation.id,
          userId: userId,
          userName: conversation.user_name,
          userAvatar: conversation.user_avatar,
          isPersonalChat: true // Flag to indicate this is a personal chat, not an AI chat
        });
      } else {
        // Fallback to direct navigation if parent navigator is not available
        navigation.navigate('ChatbotScreen', {
          conversationId: conversation.id,
          userId: userId,
          userName: conversation.user_name,
          userAvatar: conversation.user_avatar,
          isPersonalChat: true
        });
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Show an error message to the user
      Alert.alert(
        'Navigation Error',
        'Unable to open the chat. Please try again later.',
        [{ text: 'OK' }]
      );
    }
  };

  // Helper function to format timestamps
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // If today, show time
    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // If yesterday, show "Yesterday"
    if (diffDays === 1) {
      return 'Yesterday';
    }

    // If within a week, show day name
    if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }

    // Otherwise show date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  // Render a conversation item
  const renderConversation = ({ item }) => {
    // Format the timestamp
    const formattedTime = formatTimestamp(item.timestamp);

    return (
      <TouchableOpacity
        style={styles.conversationItem}
        onPress={() => handleChatPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.conversationAvatar}>
          <Image source={{ uri: item.user_avatar }} style={styles.chatAvatar} />
          {item.is_creator && (
            <View style={styles.creatorBadge}>
              <Text style={styles.creatorBadgeText}>CREATOR</Text>
            </View>
          )}
        </View>

        <View style={styles.conversationContent}>
          <View style={styles.conversationHeader}>
            <Text style={styles.conversationName}>{item.user_name}</Text>
            <Text style={styles.conversationTime}>{formattedTime}</Text>
          </View>

          <View style={styles.conversationFooter}>
            <Text
              style={[
                styles.conversationLastMessage,
                item.unread_count > 0 && styles.conversationUnreadMessage
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.last_message}
            </Text>

            {item.unread_count > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadBadgeText}>{item.unread_count}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'communities' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('communities')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'communities' && styles.activeTabButtonText
          ]}>Communities</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'inbox' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('inbox')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'inbox' && styles.activeTabButtonText
          ]}>Inbox</Text>
        </TouchableOpacity>
      </View>

      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.contentContainer}>
          {/* Communities Tab Content */}
          {activeTab === 'communities' && (
            <>
              {loading && !refreshing && !loadingMore ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={COLORS.primary} />
                  <Text style={styles.loadingText}>Loading posts...</Text>
                </View>
              ) : (
                <FlatList
                  data={posts}
                  renderItem={renderPost}
                  keyExtractor={(item) => item.id.toString()}
                  contentContainerStyle={styles.postsList}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={handleRefresh}
                      colors={[COLORS.primary]}
                    />
                  }
                  onEndReached={handleLoadMore}
                  onEndReachedThreshold={0.5}
                  ListFooterComponent={renderFooter}
                  ListEmptyComponent={renderEmptyPosts}
                  showsVerticalScrollIndicator={false}
                />
              )}

              {/* Input Field at Bottom for Communities */}
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Share something with the community..."
                  placeholderTextColor={COLORS.textSecondary}
                  value={newComment}
                  onChangeText={setNewComment}
                  multiline
                />
                <TouchableOpacity
                  style={[styles.postButton, !newComment.trim() && styles.postButtonDisabled]}
                  onPress={handlePostComment}
                  disabled={!newComment.trim()}
                >
                  <Text style={[styles.postButtonText, !newComment.trim() && styles.postButtonTextDisabled]}>Post</Text>
                </TouchableOpacity>
              </View>
            </>
          )}

          {/* Inbox Tab Content */}
          {activeTab === 'inbox' && (
            <>
              {loadingChats ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={COLORS.primary} />
                  <Text style={styles.loadingText}>Loading conversations...</Text>
                </View>
              ) : (
                <FlatList
                  data={conversations}
                  renderItem={renderConversation}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={styles.conversationsList}
                  refreshControl={
                    <RefreshControl
                      refreshing={false}
                      onRefresh={() => loadConversations()}
                      colors={[COLORS.primary]}
                    />
                  }
                  ListEmptyComponent={renderEmptyConversations}
                  showsVerticalScrollIndicator={false}
                />
              )}
            </>
          )}
        </View>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  // Tab Navigation Styles
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: COLORS.primary,
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  activeTabButtonText: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },

  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  postsList: {
    padding: 16,
    paddingBottom: 100, // Extra padding at the bottom for the input field
  },

  // Conversation List Styles
  conversationsList: {
    padding: 0,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    backgroundColor: COLORS.card,
  },
  conversationAvatar: {
    position: 'relative',
    marginRight: 12,
  },
  chatAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  creatorBadge: {
    position: 'absolute',
    bottom: -5,
    left: 0,
    right: 0,
    backgroundColor: '#333',
    paddingVertical: 2,
    paddingHorizontal: 4,
    borderRadius: 4,
    alignItems: 'center',
  },
  creatorBadgeText: {
    color: '#fff',
    fontSize: 8,
    fontWeight: 'bold',
  },
  conversationContent: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  conversationTime: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  conversationFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  conversationLastMessage: {
    fontSize: 14,
    color: COLORS.textSecondary,
    flex: 1,
    marginRight: 8,
  },
  conversationUnreadMessage: {
    color: COLORS.text,
    fontWeight: '500',
  },
  unreadBadge: {
    backgroundColor: COLORS.primary,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unreadBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  postContainer: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  postHeaderText: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 2,
  },
  timestamp: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  postText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 22,
    marginBottom: 16,
  },
  postActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
    paddingVertical: 4,
  },
  actionText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginLeft: 6,
  },
  commentsSection: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  replyInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  replyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  replyInput: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 14,
    maxHeight: 100,
  },
  sendButton: {
    padding: 8,
    marginLeft: 8,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  commentsList: {
    marginTop: 8,
  },
  commentContainer: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  commentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  commentInfo: {
    flex: 1,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  commentTimestamp: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  commentText: {
    fontSize: 14,
    color: COLORS.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  commentLikeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  commentLikeText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  noCommentsText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginVertical: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: COLORS.card,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  input: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 14,
    maxHeight: 100,
  },
  postButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    marginLeft: 12,
  },
  postButtonDisabled: {
    backgroundColor: COLORS.border,
  },
  postButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  postButtonTextDisabled: {
    color: COLORS.textSecondary,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  loadingMoreText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginLeft: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  emptyButton: {
    marginTop: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default CommunityScreen;
