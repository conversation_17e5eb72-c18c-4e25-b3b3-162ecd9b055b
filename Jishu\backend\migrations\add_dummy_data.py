"""
<PERSON><PERSON><PERSON> to add dummy data to all tables in the Jishu application.
This script populates the database with test data for development and testing purposes.
"""

from app import create_app, db
from app.models.user import User
from app.models.exam import ExamCategory, Subject, QuestionWithOptions
from app.models.user_progress import ExamAttempt, UserAnswer
from app.models.payment import Payment, Purchase, PaymentPurchase
from app.models.community import CommunityPost, CommunityComment, CommunityLike
from app.models.chat import ChatSession, ChatMessage
from app.models.notification import Notification
from datetime import datetime, timedelta, timezone
from decimal import Decimal
import random

def add_dummy_data():
    """Add dummy data to all tables"""
    app = create_app()
    with app.app_context():
        print("Adding dummy data to the database...")

        # Clear existing data (optional - comment out if you want to keep existing data)
        # clear_existing_data()

        # Add users
        users = add_users()

        # Add exam categories and subjects
        categories, subjects = add_exam_categories_and_subjects()

        # Add questions with options
        questions = add_questions_with_options(subjects)

        # Add purchases
        purchases = add_purchases(users, subjects)

        # Add exam attempts and user answers
        exam_attempts = add_exam_attempts_and_answers(purchases, questions)

        # Add payments
        payments = add_payments(users, subjects)

        # Add community posts, comments, and likes
        posts, comments = add_community_content(users)

        # Add chat sessions and messages
        chat_sessions = add_chat_sessions_and_messages(users, subjects, questions)

        # Add notifications
        add_notifications(users, exam_attempts, payments, posts)

        print("Dummy data added successfully!")

def clear_existing_data():
    """Clear existing data from all tables"""
    print("Clearing existing data...")

    # Delete in reverse order of dependencies
    Notification.query.delete()
    ChatMessage.query.delete()
    ChatSession.query.delete()
    CommunityLike.query.delete()
    CommunityComment.query.delete()
    CommunityPost.query.delete()
    PaymentPurchase.query.delete()
    Payment.query.delete()
    Purchase.query.delete()
    UserAnswer.query.delete()
    ExamAttempt.query.delete()
    QuestionWithOptions.query.delete()
    Subject.query.delete()
    ExamCategory.query.delete()
    User.query.delete()

    db.session.commit()
    print("Existing data cleared.")

def add_users():
    """Add dummy users"""
    print("Adding users...")

    users = []

    # Admin user
    admin = User(
        mobile_number="9876543210",
        name="Admin User",
        avatar="avatar1.png",
        role="admin",
        created_at=datetime.now(timezone.utc),
        last_login=datetime.now(timezone.utc),
        is_profile_complete=True
    )
    users.append(admin)

    # Regular student users
    student_data = [
        {"mobile": "9876543211", "name": "Rahul Sharma", "avatar": "avatar2.png"},
        {"mobile": "9876543212", "name": "Priya Patel", "avatar": "avatar3.png"},
        {"mobile": "9876543213", "name": "Amit Kumar", "avatar": "avatar4.png"},
        {"mobile": "9876543214", "name": "Sneha Gupta", "avatar": "avatar5.png"},
        {"mobile": "9876543215", "name": "Vikram Singh", "avatar": "avatar6.png"}
    ]

    for data in student_data:
        student = User(
            mobile_number=data["mobile"],
            name=data["name"],
            avatar=data["avatar"],
            role="student",
            created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30)),
            last_login=datetime.now(timezone.utc) - timedelta(days=random.randint(0, 5)),
            is_profile_complete=True
        )
        users.append(student)

    db.session.add_all(users)
    db.session.commit()

    print(f"Added {len(users)} users.")
    return users

def add_exam_categories_and_subjects():
    """Add exam categories and subjects"""
    print("Adding exam categories and subjects...")

    # Create exam categories
    categories = [
        ExamCategory(name="NEET", description="National Eligibility cum Entrance Test", icon="neet_icon.png"),
        ExamCategory(name="JEE", description="Joint Entrance Examination", icon="jee_icon.png"),
        ExamCategory(name="CET", description="Common Entrance Test", icon="cet_icon.png")
    ]

    db.session.add_all(categories)
    db.session.commit()

    # Create subjects for each category
    subjects = []

    # NEET subjects
    neet_subjects = [
        {"name": "Physics", "description": "NEET Physics", "icon": "physics_icon.png", "duration": 60},
        {"name": "Chemistry", "description": "NEET Chemistry", "icon": "chemistry_icon.png", "duration": 60},
        {"name": "Biology", "description": "NEET Biology", "icon": "biology_icon.png", "duration": 60},
        {"name": "Full Mock Test", "description": "NEET Full Mock Test", "icon": "mock_icon.png", "duration": 180, "is_full_mock": True}
    ]

    for subject_data in neet_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=subject_data["icon"],
            is_full_mock=subject_data.get("is_full_mock", False),
            duration_minutes=subject_data["duration"],
            category_id=categories[0].id
        )
        subjects.append(subject)

    # JEE subjects
    jee_subjects = [
        {"name": "Physics", "description": "JEE Physics", "icon": "physics_icon.png", "duration": 60},
        {"name": "Chemistry", "description": "JEE Chemistry", "icon": "chemistry_icon.png", "duration": 60},
        {"name": "Mathematics", "description": "JEE Mathematics", "icon": "math_icon.png", "duration": 60},
        {"name": "Full Mock Test", "description": "JEE Full Mock Test", "icon": "mock_icon.png", "duration": 180, "is_full_mock": True}
    ]

    for subject_data in jee_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=subject_data["icon"],
            is_full_mock=subject_data.get("is_full_mock", False),
            duration_minutes=subject_data["duration"],
            category_id=categories[1].id
        )
        subjects.append(subject)

    # CET subjects
    cet_subjects = [
        {"name": "Physics", "description": "CET Physics", "icon": "physics_icon.png", "duration": 45},
        {"name": "Chemistry", "description": "CET Chemistry", "icon": "chemistry_icon.png", "duration": 45},
        {"name": "Mathematics", "description": "CET Mathematics", "icon": "math_icon.png", "duration": 45},
        {"name": "Full Mock Test", "description": "CET Full Mock Test", "icon": "mock_icon.png", "duration": 120, "is_full_mock": True}
    ]

    for subject_data in cet_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=subject_data["icon"],
            is_full_mock=subject_data.get("is_full_mock", False),
            duration_minutes=subject_data["duration"],
            category_id=categories[2].id
        )
        subjects.append(subject)

    db.session.add_all(subjects)
    db.session.commit()

    print(f"Added {len(categories)} categories and {len(subjects)} subjects.")
    return categories, subjects

def add_questions_with_options(subjects):
    """Add questions with options for each subject"""
    print("Adding questions with options...")

    questions = []

    # For each subject, add 10 questions (except full mock tests)
    for subject in subjects:
        if not subject.is_full_mock:
            for i in range(1, 11):
                difficulty = random.choice(["easy", "medium", "hard"])
                marks = 4 if difficulty == "hard" else (3 if difficulty == "medium" else 2)
                negative_marks = 1

                question = QuestionWithOptions(
                    subject_id=subject.id,
                    text=f"Sample question {i} for {subject.name} ({subject.category.name}): What is the correct answer?",
                    difficulty=difficulty,
                    marks=marks,
                    negative_marks=negative_marks,
                    option_a=f"Option A for question {i}",
                    option_b=f"Option B for question {i}",
                    option_c=f"Option C for question {i}",
                    option_d=f"Option D for question {i}",
                    correct_option=random.choice(["A", "B", "C", "D"]),
                    explanation=f"Explanation for question {i}: This is the correct answer because...",
                    created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30))
                )
                questions.append(question)

    db.session.add_all(questions)
    db.session.commit()

    print(f"Added {len(questions)} questions with options.")
    return questions

def add_purchases(users, subjects):
    """Add purchases"""
    print("Adding purchases...")

    purchases = []

    # Skip admin user (users[0])
    for user in users[1:]:
        # Each user purchases 2-4 random subjects
        num_purchases = random.randint(2, 4)
        purchased_subjects = random.sample(subjects, num_purchases)

        for subject in purchased_subjects:
            retakes_used = random.randint(0, 3)

            purchase = Purchase(
                user_id=user.id,
                subject_id=subject.id,
                purchase_date=datetime.now(timezone.utc) - timedelta(days=random.randint(5, 30)),
                max_retakes=3,
                retake_used=retakes_used,
                marks=4,
                negative_marks=1
            )
            purchases.append(purchase)

    db.session.add_all(purchases)
    db.session.commit()

    print(f"Added {len(purchases)} purchases.")
    return purchases

def add_exam_attempts_and_answers(purchases, questions):
    """Add exam attempts and user answers"""
    print("Adding exam attempts and user answers...")

    exam_attempts = []
    user_answers = []

    for purchase in purchases:
        # Add 1-3 attempts for each purchase
        num_attempts = random.randint(1, min(3, purchase.retake_used + 1))

        for attempt_num in range(1, num_attempts + 1):
            # Get questions for this subject
            subject_questions = [q for q in questions if q.subject_id == purchase.subject_id]

            if not subject_questions:
                continue

            # Randomly select questions for this attempt
            num_questions = min(len(subject_questions), 10)
            selected_questions = random.sample(subject_questions, num_questions)

            # Calculate attempt statistics
            correct_answers = random.randint(0, num_questions)
            wrong_answers = random.randint(0, num_questions - correct_answers)
            unattempted = num_questions - correct_answers - wrong_answers

            # Calculate score using the marks from the purchase
            score = 0
            for _ in range(correct_answers):
                score += purchase.marks
            for _ in range(wrong_answers):
                score -= purchase.negative_marks

            # Create attempt
            started_at = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 10))
            time_taken = random.randint(15, 60) * 60  # 15-60 minutes in seconds

            attempt = ExamAttempt(
                user_id=purchase.user_id,
                purchase_id=purchase.id,
                attempt_number=attempt_num,
                score=score,
                total_questions=num_questions,
                correct_answers=correct_answers,
                wrong_answers=wrong_answers,
                unattempted=unattempted,
                time_taken_seconds=time_taken,
                started_at=started_at,
                completed_at=started_at + timedelta(seconds=time_taken)
            )

            db.session.add(attempt)
            db.session.flush()  # Get the attempt ID

            exam_attempts.append(attempt)

            # Add user answers for this attempt
            for i, question in enumerate(selected_questions):
                is_correct = i < correct_answers
                is_wrong = correct_answers <= i < (correct_answers + wrong_answers)
                is_unattempted = i >= (correct_answers + wrong_answers)

                if is_unattempted:
                    # No answer for unattempted questions
                    continue

                selected_option = question.correct_option if is_correct else random.choice([opt for opt in ["A", "B", "C", "D"] if opt != question.correct_option])
                is_marked_for_review = random.choice([True, False]) if random.random() < 0.2 else False

                answer = UserAnswer(
                    attempt_id=attempt.id,
                    question_id=question.id,
                    selected_option=selected_option,
                    is_marked_for_review=is_marked_for_review,
                    time_spent_seconds=random.randint(30, 180),
                    created_at=started_at + timedelta(seconds=random.randint(60, time_taken - 60))
                )

                user_answers.append(answer)

    db.session.add_all(user_answers)
    db.session.commit()

    print(f"Added {len(exam_attempts)} exam attempts and {len(user_answers)} user answers.")
    return exam_attempts

def add_payments(users, subjects):
    """Add payments and payment purchases"""
    print("Adding payments and payment purchases...")

    payments = []
    payment_purchases = []

    # Skip admin user (users[0])
    for user in users[1:]:
        # Add 1-3 payments for each user
        num_payments = random.randint(1, 3)

        for _ in range(num_payments):
            # Randomly select 1-3 subjects for this payment
            num_subjects = random.randint(1, 3)
            selected_subjects = random.sample(subjects, num_subjects)

            # Calculate payment amount
            total_amount = Decimal('0.00')

            # Create payment
            payment_date = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30))
            status = random.choice(["completed", "completed", "completed", "pending", "failed"])

            payment = Payment(
                user_id=user.id,
                amount=Decimal('0.00'),  # Will update after adding purchases
                currency="INR",
                payment_method=random.choice(["credit_card", "debit_card", "upi", "net_banking"]),
                transaction_id=f"TXN{random.randint(100000, 999999)}",
                status=status,
                payment_date=payment_date,
                created_at=payment_date,
                updated_at=payment_date + timedelta(minutes=random.randint(1, 10))
            )

            db.session.add(payment)
            db.session.flush()  # Get the payment ID

            payments.append(payment)

            # Create purchases for each subject and link them to the payment
            for subject in selected_subjects:
                price = Decimal(str(random.randint(499, 1499)))
                total_amount += price

                # Create a purchase
                purchase = Purchase(
                    user_id=user.id,
                    subject_id=subject.id,
                    purchase_date=payment_date,
                    max_retakes=3,
                    retake_used=random.randint(0, 2),
                    marks=4,
                    negative_marks=1,
                    created_at=payment_date,
                    updated_at=payment_date
                )

                db.session.add(purchase)
                db.session.flush()  # Get the purchase ID

                # Create payment_purchase association
                payment_purchase = PaymentPurchase(
                    payment_id=payment.id,
                    purchase_id=purchase.id,
                    amount=price,
                    created_at=payment_date
                )

                payment_purchases.append(payment_purchase)

            # Update payment amount
            payment.amount = total_amount

    db.session.add_all(payment_purchases)
    db.session.commit()

    print(f"Added {len(payments)} payments and {len(payment_purchases)} payment purchases.")
    return payments

def add_community_content(users):
    """Add community posts, comments, and likes"""
    print("Adding community content...")

    posts = []
    comments = []
    likes = []

    # Create 10-15 posts
    num_posts = random.randint(10, 15)

    for i in range(1, num_posts + 1):
        # Random user (including admin)
        user = random.choice(users)

        post = CommunityPost(
            user_id=user.id,
            title=f"Sample Post {i}",
            content=f"This is the content of post {i}. It contains some text about studying for exams.",
            image_url=f"post_image_{i}.jpg" if random.random() < 0.3 else None,
            likes_count=0,  # Will update after adding likes
            comments_count=0,  # Will update after adding comments
            created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30)),
            updated_at=datetime.now(timezone.utc) - timedelta(days=random.randint(0, 1))
        )

        db.session.add(post)
        db.session.flush()  # Get the post ID

        posts.append(post)

        # Add 0-5 comments for each post
        num_comments = random.randint(0, 5)
        post_comments = []

        for j in range(1, num_comments + 1):
            # Random user (including admin)
            comment_user = random.choice(users)

            comment = CommunityComment(
                post_id=post.id,
                user_id=comment_user.id,
                content=f"This is comment {j} on post {i}.",
                likes_count=0,  # Will update after adding likes
                created_at=post.created_at + timedelta(hours=random.randint(1, 48)),
                updated_at=post.created_at + timedelta(hours=random.randint(1, 48))
            )

            db.session.add(comment)
            db.session.flush()  # Get the comment ID

            post_comments.append(comment)
            comments.append(comment)

        # Update post comments count
        post.comments_count = len(post_comments)

        # Add likes for the post
        num_post_likes = random.randint(0, len(users))
        post_likers = random.sample(users, num_post_likes)

        for liker in post_likers:
            like = CommunityLike(
                user_id=liker.id,
                post_id=post.id,
                comment_id=None,
                created_at=post.created_at + timedelta(hours=random.randint(1, 72))
            )

            likes.append(like)

        # Update post likes count
        post.likes_count = num_post_likes

        # Add likes for comments
        for comment in post_comments:
            num_comment_likes = random.randint(0, len(users) // 2)
            comment_likers = random.sample(users, num_comment_likes)

            for liker in comment_likers:
                # Avoid duplicate likes from the same user
                if not any(l for l in likes if l.user_id == liker.id and l.comment_id == comment.id):
                    like = CommunityLike(
                        user_id=liker.id,
                        post_id=None,
                        comment_id=comment.id,
                        created_at=comment.created_at + timedelta(hours=random.randint(1, 24))
                    )

                    likes.append(like)

            # Update comment likes count
            comment.likes_count = num_comment_likes

    db.session.add_all(likes)
    db.session.commit()

    print(f"Added {len(posts)} posts, {len(comments)} comments, and {len(likes)} likes.")
    return posts, comments

def add_chat_sessions_and_messages(users, subjects, questions):
    """Add chat sessions and messages"""
    print("Adding chat sessions and messages...")

    chat_sessions = []
    chat_messages = []

    # Skip admin user (users[0])
    for user in users[1:]:
        # Add 1-3 chat sessions for each user
        num_sessions = random.randint(1, 3)

        for i in range(1, num_sessions + 1):
            # Randomly associate with a subject or not
            subject = random.choice(subjects) if random.random() < 0.7 else None
            subject_id = subject.id if subject else None

            session = ChatSession(
                user_id=user.id,
                title=f"Chat Session {i}" if not subject else f"Help with {subject.name}",
                subject_id=subject_id,
                is_active=random.choice([True, False]),
                created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30)),
                updated_at=datetime.now(timezone.utc) - timedelta(days=random.randint(0, 1))
            )

            db.session.add(session)
            db.session.flush()  # Get the session ID

            chat_sessions.append(session)

            # Add 5-15 messages for each session
            num_messages = random.randint(5, 15)

            for j in range(1, num_messages + 1):
                # Alternate between user and bot messages
                is_bot = (j % 2 == 0)

                # Randomly associate with a question or not
                related_question = None
                if subject and is_bot and random.random() < 0.3:
                    subject_questions = [q for q in questions if q.subject_id == subject_id]
                    if subject_questions:
                        related_question = random.choice(subject_questions)

                message_text = ""
                if is_bot:
                    if related_question:
                        message_text = f"Here's a practice question: {related_question.text}\n\nOptions:\nA: {related_question.option_a}\nB: {related_question.option_b}\nC: {related_question.option_c}\nD: {related_question.option_d}"
                    else:
                        message_text = f"This is bot message {j} in session {i}. I can help you with your studies."
                else:
                    message_text = f"This is user message {j} in session {i}. I need help with a concept."

                message = ChatMessage(
                    session_id=session.id,
                    user_id=user.id,
                    is_bot=is_bot,
                    message=message_text,
                    related_question_id=related_question.id if related_question else None,
                    created_at=session.created_at + timedelta(minutes=j * 2)
                )

                chat_messages.append(message)

    db.session.add_all(chat_messages)
    db.session.commit()

    print(f"Added {len(chat_sessions)} chat sessions and {len(chat_messages)} chat messages.")
    return chat_sessions

def add_notifications(users, exam_attempts, payments, posts):
    """Add notifications"""
    print("Adding notifications...")

    notifications = []

    # Exam result notifications
    for attempt in exam_attempts:
        notification = Notification(
            user_id=attempt.user_id,
            title="Exam Result",
            message=f"Your exam result is ready. You scored {attempt.score} out of {attempt.total_questions * 4}.",
            notification_type="exam_result",
            related_id=attempt.id,
            is_read=random.choice([True, False]),
            created_at=attempt.completed_at + timedelta(minutes=random.randint(1, 10))
        )
        notifications.append(notification)

    # Payment notifications
    for payment in payments:
        if payment.status == "completed":
            notification = Notification(
                user_id=payment.user_id,
                title="Payment Successful",
                message=f"Your payment of ₹{payment.amount} was successful.",
                notification_type="payment",
                related_id=payment.id,
                is_read=random.choice([True, False]),
                created_at=payment.updated_at + timedelta(minutes=random.randint(1, 5))
            )
            notifications.append(notification)
        elif payment.status == "failed":
            notification = Notification(
                user_id=payment.user_id,
                title="Payment Failed",
                message=f"Your payment of ₹{payment.amount} failed. Please try again.",
                notification_type="payment",
                related_id=payment.id,
                is_read=random.choice([True, False]),
                created_at=payment.updated_at + timedelta(minutes=random.randint(1, 5))
            )
            notifications.append(notification)

    # Community notifications (for post comments)
    for post in posts:
        if post.comments_count > 0:
            notification = Notification(
                user_id=post.user_id,
                title="New Comment",
                message=f"Someone commented on your post: {post.title}",
                notification_type="community",
                related_id=post.id,
                is_read=random.choice([True, False]),
                created_at=post.updated_at + timedelta(minutes=random.randint(1, 60))
            )
            notifications.append(notification)

    # System notifications
    for user in users[1:]:  # Skip admin
        notification = Notification(
            user_id=user.id,
            title="Welcome to Jishu",
            message="Welcome to Jishu! Start exploring our exam preparation resources.",
            notification_type="system",
            related_id=None,
            is_read=random.choice([True, False]),
            created_at=user.created_at + timedelta(minutes=random.randint(5, 30))
        )
        notifications.append(notification)

    db.session.add_all(notifications)
    db.session.commit()

    print(f"Added {len(notifications)} notifications.")

if __name__ == "__main__":
    add_dummy_data()
