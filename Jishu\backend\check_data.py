import pymysql
pymysql.install_as_MySQLdb()

from app import create_app, db
from app.models.user import User
from app.models.exam import ExamCategory, Subject, QuestionWithOptions
from app.models.user_progress import ExamAttempt, UserAnswer
from app.models.payment import Payment, Purchase, PaymentPurchase

app = create_app()

with app.app_context():
    # Check users
    users = db.session.query(User).all()
    print(f"Total users: {len(users)}")
    for user in users[:3]:  # Show first 3 users
        print(f"  - {user.name} ({user.mobile_number}), role: {user.role}")

    # Check exam categories
    categories = db.session.query(ExamCategory).all()
    print(f"\nTotal exam categories: {len(categories)}")
    for category in categories:
        print(f"  - {category.name}: {category.description}")

    # Check subjects
    subjects = db.session.query(Subject).all()
    print(f"\nTotal subjects: {len(subjects)}")
    for subject in subjects[:5]:  # Show first 5 subjects
        print(f"  - {subject.name} ({subject.category.name}): {subject.description}")

    # Check questions
    questions = db.session.query(QuestionWithOptions).all()
    print(f"\nTotal questions: {len(questions)}")
    for question in questions[:2]:  # Show first 2 questions
        print(f"  - Q: {question.text}")
        print(f"    A: {question.option_a}, B: {question.option_b}, C: {question.option_c}, D: {question.option_d}")
        print(f"    Correct: {question.correct_option}")

    # Check purchases
    purchases = db.session.query(Purchase).all()
    print(f"\nTotal purchases: {len(purchases)}")

    # Check exam attempts
    exam_attempts = db.session.query(ExamAttempt).all()
    print(f"Total exam attempts: {len(exam_attempts)}")

    # Check user answers
    user_answers = db.session.query(UserAnswer).all()
    print(f"Total user answers: {len(user_answers)}")

    # Check payments
    payments = db.session.query(Payment).all()
    print(f"Total payments: {len(payments)}")

    # Check payment purchases
    payment_purchases = db.session.query(PaymentPurchase).all()
    print(f"Total payment purchases: {len(payment_purchases)}")
