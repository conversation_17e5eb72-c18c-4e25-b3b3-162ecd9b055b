from flask import jsonify, request, current_app
from app.api import bp
from app.models.user import User
from app import db
from flask_jwt_extended import (
    create_access_token, create_refresh_token,
    jwt_required, get_jwt_identity
)
from datetime import datetime, timedelta, timezone

# Request OTP
@bp.route('/api/auth/request-otp', methods=['POST'])
def request_otp():
    """
    Request an OTP for login/registration
    For development, we'll use a hardcoded OTP: 123456
    """
    data = request.get_json() or {}

    if 'mobile_number' not in data:
        return jsonify({'error': 'Mobile number is required'}), 400

    mobile_number = data['mobile_number']

    # Check if user exists
    user = User.query.filter_by(mobile_number=mobile_number).first()

    # For development, we'll use a hardcoded OTP
    otp = '123456'

    # In a real app, we would send the OTP to the user's mobile number
    # and store it in the database with an expiration time

    return jsonify({
        'success': True,
        'message': 'OTP sent successfully',
        'is_new_user': user is None,
        'otp': otp  # In a real app, we wouldn't return the OTP
    })

# Verify OTP
@bp.route('/api/auth/verify-otp', methods=['POST'])
def verify_otp():
    """
    Verify the OTP and login/register the user
    For development, we'll accept any OTP that matches the hardcoded value: 123456
    """
    data = request.get_json() or {}

    if 'mobile_number' not in data or 'otp' not in data:
        return jsonify({'error': 'Mobile number and OTP are required'}), 400

    mobile_number = data['mobile_number']
    otp = data['otp']

    # For development, we'll accept any OTP that matches the hardcoded value
    if otp != '123456':
        return jsonify({'error': 'Invalid OTP'}), 400

    # Check if user exists
    user = User.query.filter_by(mobile_number=mobile_number).first()
    is_new_user = user is None

    # If user doesn't exist, create a new one
    if is_new_user:
        user = User(
            mobile_number=mobile_number,
            name=None,
            icon=None,
            role='student',
            is_profile_complete=False,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(user)
        db.session.commit()

    # Update last login
    user.last_login = datetime.now(timezone.utc)
    db.session.commit()

    # Create tokens
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)

    return jsonify({
        'success': True,
        'message': 'OTP verified successfully',
        'is_new_user': is_new_user,
        'user': {
            'id': user.id,
            'mobile_number': user.mobile_number,
            'name': user.name,
            'icon': user.icon,
            'role': user.role,
            'is_profile_complete': user.is_profile_complete,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None
        },
        'access_token': access_token,
        'refresh_token': refresh_token
    })

# Complete profile
@bp.route('/api/auth/complete-profile', methods=['POST'])
def complete_profile():
    """
    Complete the user profile after registration
    """
    data = request.get_json() or {}

    # Log the received data for debugging
    print(f"API auth complete-profile received data: {data}")

    if 'mobile_number' not in data or 'name' not in data:
        return jsonify({'error': 'Mobile number and name are required'}), 400

    mobile_number = data['mobile_number']
    name = data['name']

    # Accept either 'icon' or 'avatar' field for compatibility
    icon = data.get('icon', None)
    if icon is None:
        icon = data.get('avatar', 'avatar1.png')  # Default avatar

    print(f"Using icon/avatar: {icon}")

    # Find the user
    user = User.query.filter_by(mobile_number=mobile_number).first()

    if not user:
        print(f"User not found with mobile number: {mobile_number}")
        return jsonify({'error': 'User not found'}), 404

    print(f"Found user: {user.id}, {user.mobile_number}, current name: {user.name}, current icon: {user.icon}")

    # Update user profile
    user.name = name
    user.icon = icon
    user.is_profile_complete = True

    try:
        db.session.commit()
        print(f"User profile updated successfully: {user.id}, {user.name}, {user.icon}, is_profile_complete: {user.is_profile_complete}")
    except Exception as e:
        db.session.rollback()
        print(f"Error updating user profile: {e}")
        return jsonify({'error': f'Database error: {str(e)}'}), 500

    # Create tokens
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)

    response_data = {
        'success': True,
        'message': 'Profile completed successfully',
        'user': {
            'id': user.id,
            'mobile_number': user.mobile_number,
            'name': user.name,
            'icon': user.icon,
            'role': user.role,
            'is_profile_complete': user.is_profile_complete,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None
        },
        'access_token': access_token,
        'refresh_token': refresh_token
    }

    print(f"Sending response: {response_data}")
    return jsonify(response_data)

# Get current user
@bp.route('/api/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get the current user's profile
    """
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': 'User not found'}), 404

    return jsonify({
        'id': user.id,
        'mobile_number': user.mobile_number,
        'name': user.name,
        'icon': user.icon,
        'role': user.role,
        'is_profile_complete': user.is_profile_complete,
        'created_at': user.created_at.isoformat() if user.created_at else None,
        'last_login': user.last_login.isoformat() if user.last_login else None
    })

# Update profile
@bp.route('/api/auth/update-profile', methods=['POST'])
@jwt_required()
def update_profile():
    """
    Update the user's profile
    """
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': 'User not found'}), 404

    data = request.get_json() or {}

    # Update user profile
    if 'name' in data:
        user.name = data['name']
    if 'icon' in data:
        user.icon = data['icon']

    db.session.commit()

    return jsonify({
        'success': True,
        'message': 'Profile updated successfully',
        'user': {
            'id': user.id,
            'mobile_number': user.mobile_number,
            'name': user.name,
            'icon': user.icon,
            'role': user.role,
            'is_profile_complete': user.is_profile_complete,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None
        }
    })

# Refresh token
@bp.route('/api/auth/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """
    Refresh the access token
    """
    current_user_id = get_jwt_identity()
    access_token = create_access_token(identity=current_user_id)

    return jsonify({
        'access_token': access_token
    })

# Check if user exists
@bp.route('/api/auth/check-user', methods=['GET'])
def check_user():
    """
    Check if a user exists by mobile number
    """
    mobile_number = request.args.get('mobile')

    if not mobile_number:
        return jsonify({'error': 'Mobile number is required'}), 400

    user = User.query.filter_by(mobile_number=mobile_number).first()

    return jsonify({
        'exists': user is not None
    })
