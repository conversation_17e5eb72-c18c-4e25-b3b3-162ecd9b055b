import os
from flask import Flask, jsonify, request

# Initialize app
app = Flask('app')

# Try to import optional dependencies
try:
    from sentence_transformers import SentenceTransformer
    embedding_model = SentenceTransformer('all-MiniLM-L6-v2')  # Open-source model
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: sentence_transformers not available. Some features will be limited.")
    embedding_model = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    print("Warning: PyPDF2 not available. PDF processing will not work.")
    PYPDF2_AVAILABLE = False

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    print("Warning: ollama not available. MCQ generation will not work.")
    OLLAMA_AVAILABLE = False
PDF_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pdfs')
texts = []
sources = []
embeddings = []

# Step 1: Extract Text from PDFs
def extract_texts_from_pdfs(folder_path):
    if not PYPDF2_AVAILABLE:
        print("Cannot extract text from PDFs: PyPDF2 not available")
        return []

    # Check if the folder exists
    if not os.path.exists(folder_path):
        print(f"Warning: PDF folder '{folder_path}' does not exist. Creating it.")
        os.makedirs(folder_path, exist_ok=True)
        return []

    all_text = []
    for filename in os.listdir(folder_path):
        if filename.endswith('.pdf'):
            pdf_path = os.path.join(folder_path, filename)
            reader = PdfReader(pdf_path)
            text = ''
            for page in reader.pages:
                text += page.extract_text() or ''
            all_text.append((text, filename))
    return all_text

# Step 2: Create embeddings for text data
def create_embeddings(text_data):
    texts = [item[0] for item in text_data]
    sources = [item[1] for item in text_data]

    if not SENTENCE_TRANSFORMERS_AVAILABLE:
        print("Cannot create embeddings: sentence_transformers not available")
        return None, texts, sources

    text_embeddings = embedding_model.encode(texts, convert_to_tensor=False)
    return text_embeddings, texts, sources

# Step 3: Load or Create embeddings
def load_or_create_index():
    data = extract_texts_from_pdfs(PDF_FOLDER)
    if not data:
        return None, [], []
    text_embeddings, texts, sources = create_embeddings(data)
    return text_embeddings, texts, sources

# Step 4: Generate MCQ via Ollama from combined content
def generate_mcq_from_combined_content(all_texts, sources, max_content_length=8000):
    if not OLLAMA_AVAILABLE:
        print("Cannot generate MCQs: ollama not available")
        return "MCQ generation not available. Please install ollama package."

    # Combine content from multiple PDFs, keeping track of sources
    combined_content = ""
    used_sources = []

    for i, (text, source) in enumerate(zip(all_texts, sources)):
        if len(combined_content) + len(text[:1000]) < max_content_length:
            combined_content += f"\n--- From {source} ---\n{text[:1000]}\n"
            used_sources.append(source)
        else:
            break

    prompt = f"""Based on the following content from multiple educational documents, generate 5 comprehensive multiple-choice questions with 4 options each. Mark the correct answer clearly.

Make sure the questions cover different topics and difficulty levels from the provided content.

Content from multiple sources:
\"\"\"
{combined_content}
\"\"\"

Format each question as:
Question X: [Question text]
A) [Option A]
B) [Option B]
C) [Option C]
D) [Option D]
Correct Answer: [Letter]

Sources used: {', '.join(used_sources)}
"""
    response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
    return response['message']['content'], used_sources

# Endpoint: Create Vector Index & Generate MCQs
@app.route('/generate-mcq', methods=['GET', 'POST'])
def generate_mcq_from_pdf():
    global texts, sources, embeddings

    embeddings, texts, sources = load_or_create_index()

    if not texts:
        return jsonify({'status': 'error', 'message': 'No PDF files found in the pdfs directory'})

    try:
        # Generate 5 MCQs from combined content of all PDFs
        mcq_content, used_sources = generate_mcq_from_combined_content(texts, sources)

        result = {
            'status': 'success',
            'mcq_content': mcq_content,
            'sources_used': used_sources,
            'total_pdfs_processed': len(texts),
            'total_sources_used_for_mcq': len(used_sources)
        }

    except Exception as e:
        result = {
            'status': 'error',
            'message': f'Error generating MCQs: {str(e)}',
            'total_pdfs_processed': len(texts)
        }

    return jsonify(result)

# Simple endpoint to check if the application is running
@app.route('/', methods=['GET'])
def index():
    status = {
        'status': 'running',
        'dependencies': {
            'sentence_transformers': SENTENCE_TRANSFORMERS_AVAILABLE,
            'PyPDF2': PYPDF2_AVAILABLE,
            'ollama': OLLAMA_AVAILABLE
        }
    }
    return jsonify(status)

if __name__ == '__main__':
    app.run(debug=True)
