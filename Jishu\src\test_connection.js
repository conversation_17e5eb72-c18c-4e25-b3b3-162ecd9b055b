/**
 * Test script to check which IP address works for connecting to the backend
 * Run this with: node test_connection.js
 */

const fetch = require('node-fetch');

// IP addresses to test
const ipAddresses = [
  'http://localhost:5000',
  'http://127.0.0.1:5000',
  'http://***********:5000',
  'http://********:5000'
];

// Test endpoint
const endpoint = '/api/exams/categories';

// Test each IP address
async function testConnections() {
  console.log('Testing connections to backend server...');
  
  for (const baseUrl of ipAddresses) {
    const url = `${baseUrl}${endpoint}`;
    console.log(`\nTesting ${url}...`);
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000 // 5 second timeout
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS: ${baseUrl} is working!`);
        console.log(`Received ${data.length} exam categories`);
      } else {
        console.log(`❌ FAILED: ${baseUrl} returned status ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ERROR: ${baseUrl} - ${error.message}`);
    }
  }
}

testConnections();
