# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 18ms
generate_cxx_metadata completed in 29ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 25ms
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 64ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 85ms
  [gap of 28ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 156ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 27ms
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 70ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 20ms
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
  [gap of 14ms]
generate_cxx_metadata completed in 29ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 84ms
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 140ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 34ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 84ms

