# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 28ms
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 33ms
  write-metadata-json-to-file 28ms
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 29ms
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 70ms

