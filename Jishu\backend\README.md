# Jishu Backend

## Database Structure Update

The database structure for payments has been updated with new tables replacing the old tables:

### New Tables
- `purchases`: Stores information about subject purchases by users
- `payments`: Stores payment information
- `payment_purchases`: Junction table linking payments to purchases

### Old Tables (Replaced)
- `payment_items`: Replaced by `payment_purchases`
- `payments`: Updated with new structure
- `user_exams`: Replaced by `purchases`

## API Endpoints

### Payments API

#### GET /api/payments
- Get all payments made by the current user

#### GET /api/payments/{payment_id}
- Get details of a specific payment

#### GET /api/purchases
- Get all purchases made by the current user

#### GET /api/purchases/{purchase_id}
- Get details of a specific purchase

#### POST /api/subjects/{subject_id}/purchase
- Purchase a subject and create a payment record

#### PUT /api/payments/{payment_id}/update-status
- Update the status of a payment

### User Progress API

#### GET /api/user/exams
- Get all exams purchased by the current user

#### POST /api/user/exams/{purchase_id}/start
- Start a new exam attempt

#### POST /api/user/attempts/{attempt_id}/submit
- Submit an exam attempt with answers

#### GET /api/user/attempts
- Get all exam attempts by the current user

#### GET /api/user/attempts/{attempt_id}
- Get details of a specific exam attempt

#### GET /api/user/purchases/{purchase_id}/attempts
- Get all attempts for a specific purchase

## Database Migration

To update the database schema, run the migration script:

```
python migrations/direct_db_migration.py
```

When prompted, enter `upgrade` to update the database schema.

## Adding Dummy Data

To add dummy data with the new database structure, run:

```
python scripts/add_new_dummy_data.py
```

## Models

### Purchase
- Represents a subject purchased by a user
- Includes information about retakes, marks, and negative marks

### Payment
- Represents a payment made by a user
- Can be linked to multiple purchases

### PaymentPurchase
- Junction table linking payments to purchases
- Includes the amount paid for each purchase

### ExamAttempt
- Now linked to a purchase instead of a user_exam
- Includes information about the attempt, score, and questions

### UserAnswer
- Represents a user's answer to a question in an exam attempt
- Linked to an exam attempt
