import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Animated,
  RefreshControl
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { subjectsAPI, examCategoriesAPI } from '../services/api';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336'
};

// Define consistent fonts
const FONTS = {
  regular: {
    fontFamily: 'System',
    fontWeight: 'normal',
  },
  medium: {
    fontFamily: 'System',
    fontWeight: '500',
  },
  bold: {
    fontFamily: 'System',
    fontWeight: 'bold',
  }
};

const FullExamListScreen = ({ route }) => {
  const navigation = useNavigation();
  const [subscribedExams, setSubscribedExams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0];
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [subjects, setSubjects] = useState([]);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [examCategories, setExamCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);


  useEffect(() => {
    console.log('FullExamListScreen useEffect running');
    const loadInitialData = async () => {
      try {
        setLoading(true);
        await Promise.all([
          loadSubscribedExams(),
          loadExamCategories()
        ]);

        // Check if we have a selected category from navigation params
        // Only process this if we're not coming directly from the bottom tab navigation
        const currentRoute = navigation.getState().routes[navigation.getState().index];
        const isFromBottomTab = currentRoute.name === 'Exams' && !route.params;

        if (!isFromBottomTab && route.params?.selectedCategoryId && route.params?.selectedCategoryName) {
          console.log('Found selected category in params:', route.params.selectedCategoryId, route.params.selectedCategoryName);
          handleCategoryPress(route.params.selectedCategoryId, route.params.selectedCategoryName);

          // Make sure the header is shown with the category name
          navigation.setOptions({
            headerShown: true,
            title: route.params.selectedCategoryName,
            headerLeft: () => (
              <TouchableOpacity
                style={{ marginLeft: 10, padding: 8 }}
                onPress={() => {
                  resetToCategoriesView();
                }}
              >
                <MaterialIcons name="arrow-back" size={24} color={COLORS.text} />
              </TouchableOpacity>
            )
          });
        }

        setLoading(false);
      } catch (error) {
        console.error('Error loading initial data:', error);
        setLoading(false);
      }
    };

    loadInitialData();
  }, [route.params]);

  // Refresh data when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('FullExamListScreen focused, refreshing data...');

      // Check if we're coming from the bottom tab navigation
      // If the route name is 'Exams', we're likely coming from the bottom tab
      const currentRoute = navigation.getState().routes[navigation.getState().index];
      const isFromBottomTab = currentRoute.name === 'Exams' && !route.params;

      if (isFromBottomTab) {
        console.log('Coming from bottom tab navigation, resetting to categories view');
        // Reset to categories view
        resetToCategoriesView();
      }

      // Refresh the exam categories data
      loadExamCategories();
    });

    return unsubscribe;
  }, [navigation]);

  // Set initial screen title and configure header
  useEffect(() => {
    // Only set the title if we don't have a selected category
    if (!selectedCategory) {
      navigation.setOptions({
        title: 'Exam Categories',
        headerShown: true,
        headerLeft: undefined // Use default back button if needed
      });
    }
  }, [selectedCategory, navigation]);

  const loadSubscribedExams = async () => {
    try {
      setLoading(true);
      // In the future, this should fetch from the API instead of AsyncStorage
      const storedExams = await AsyncStorage.getItem('subscribedExams');
      if (storedExams) {
        setSubscribedExams(JSON.parse(storedExams));
      } else {
        // Set empty array instead of default data
        setSubscribedExams([]);
      }
      setLoading(false);

      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }).start();

    } catch (error) {
      console.error('Error loading subscribed exams:', error);
      setSubscribedExams([]);
      setLoading(false);
    }
  };

  // Function to fetch exam categories from the backend - same as Dashboard
  const loadExamCategories = async () => {
    try {
      setLoadingCategories(true);
      console.log('Fetching exam categories...');
      const response = await examCategoriesAPI.getAll();
      console.log('Exam categories loaded:', response);

      // Check if response is an array
      if (Array.isArray(response)) {
        console.log(`Found ${response.length} exam categories`);
        setExamCategories(response);
      } else {
        console.log('Invalid response format, setting empty array');
        setExamCategories([]);
      }

      setLoadingCategories(false);
      return response;
    } catch (error) {
      console.error('Error loading exam categories:', error);
      setExamCategories([]);
      setLoadingCategories(false);
      return [];
    }
  };

  // getCategoryIcon function removed as it's no longer needed

  // CategoryCard component for grid layout
  const CategoryCard = ({ title, price, rating, reviews, onPress }) => {
    // Get icon based on category name
    const getIconComponent = () => {
      switch (title.toLowerCase()) {
        case 'neet':
        case 'medicine':
        case 'medical':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#FFF9E6' }]}>
              <MaterialCommunityIcons name="stethoscope" size={32} color="#FF9500" />
            </View>
          );
        case 'jee':
        case 'engineering':
        case 'chemistry':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6F9F0' }]}>
              <MaterialCommunityIcons name="flask" size={32} color="#4CAF50" />
            </View>
          );
        case 'gate':
        case 'biology':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6F0FF' }]}>
              <MaterialCommunityIcons name="dna" size={32} color="#2196F3" />
            </View>
          );
        case 'mathematics':
        case 'math':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#FFE6E6' }]}>
              <MaterialCommunityIcons name="chart-pie" size={32} color="#F44336" />
            </View>
          );
        case 'language':
        case 'english':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6E6FF' }]}>
              <MaterialCommunityIcons name="translate" size={32} color="#673AB7" />
            </View>
          );
        default:
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#F0F0F0' }]}>
              <MaterialCommunityIcons name="school" size={32} color="#3461eb" />
            </View>
          );
      }
    };

    return (
      <TouchableOpacity style={styles.categoryCard} onPress={onPress} activeOpacity={0.7}>
        {getIconComponent()}
        <Text style={styles.categoryTitle}>{title}</Text>
        <Text style={styles.categoryPrice}>{price}</Text>
        <View style={styles.ratingContainer}>
          <MaterialIcons name="star" size={14} color="#FFC107" />
          <Text style={styles.ratingText}>{rating}</Text>
          <Text style={styles.reviewsText}>({reviews} reviews)</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadSubscribedExams(), loadExamCategories()]);
    setRefreshing(false);
  };

  // Map subject names to appropriate icons
  const getIconForSubject = (subjectName) => {
    const name = subjectName.toLowerCase();
    if (name.includes('physics')) return 'atom';
    if (name.includes('chemistry')) return 'flask-empty-outline';
    if (name.includes('biology') || name.includes('botany')) return 'virus-outline';
    if (name.includes('zoology')) return 'dna';
    if (name.includes('math')) return 'calculator-variant-outline';
    if (name.includes('full') || name.includes('mock')) return 'stethoscope';
    if (name.includes('computer')) return 'laptop';
    if (name.includes('electronics')) return 'chip';
    return 'book-open-variant'; // Default icon
  };

  const fetchSubjectsForCategory = async (categoryId, categoryName) => {
    setLoadingSubjects(true);
    try {
      // Log the received categoryId for debugging
      console.log('Received categoryId:', categoryId, 'Type:', typeof categoryId, 'Category Name:', categoryName);

      // Ensure categoryId is valid and properly formatted
      let categoryIdParam;
      if (typeof categoryId === 'number') {
        categoryIdParam = String(categoryId);
      } else if (typeof categoryId === 'string' && !isNaN(Number(categoryId))) {
        categoryIdParam = categoryId;
      } else {
        console.warn('Invalid categoryId received:', categoryId);
        // Use a default category ID for testing
        categoryIdParam = '1';
      }

      console.log('Using categoryIdParam for API call:', categoryIdParam);

      // Use the getByCategory method which maps to our Flask API endpoint
      const response = await subjectsAPI.getByCategory(categoryIdParam);
      console.log('Subjects fetched:', response);

      // Check if response is an array
      if (Array.isArray(response)) {
        console.log(`Found ${response.length} subjects for category ${categoryName}`);

        if (response.length > 0) {
          // Format the subjects data
          const formattedSubjects = response.map(subject => ({
            id: subject.id,
            name: subject.name,
            description: subject.description || `${categoryName} ${subject.name}`,
            icon: getIconForSubject(subject.name),
            isFullMock: subject.is_full_mock || false,
            duration_minutes: subject.duration_minutes || 60,
            category_id: subject.category_id || categoryIdParam,
            categoryName: categoryName
          }));

          // Add a Full Mock Exam option if not already present
          const hasMockExam = formattedSubjects.some(s => s.isFullMock);
          if (!hasMockExam && formattedSubjects.length > 0) {
            formattedSubjects.push({
              id: 'full_mock',
              name: 'Full Mock Exam',
              description: `Complete ${categoryName} exam pattern`,
              icon: 'stethoscope',
              isFullMock: true,
              duration_minutes: 180, // Default 3 hours for full mock
              category_id: categoryIdParam,
              categoryName: categoryName
            });
          }

          setSubjects(formattedSubjects);
        } else {
          console.log('No subjects found for this category');
          setSubjects([]);
        }
      } else {
        console.log('Invalid response format, setting empty array');
        setSubjects([]);
      }

      setLoadingSubjects(false);
    } catch (err) {
      console.error('Error fetching subjects:', err);
      setSubjects([]);
      setLoadingSubjects(false);
    }
  };

  const handleCategoryPress = (category, categoryName) => {
    console.log('handleCategoryPress called with:', category, categoryName);

    // Ensure we have a valid category ID
    const categoryId = typeof category === 'object' ? category.id : category;

    // Set the selected category state
    setSelectedCategory({
      id: categoryId,
      name: categoryName
    });

    // Update the navigation header title and show custom back button
    navigation.setOptions({
      headerShown: true,
      title: categoryName,
      headerLeft: () => (
        <TouchableOpacity
          style={{ marginLeft: 10, padding: 8 }}
          onPress={() => {
            resetToCategoriesView();
          }}
        >
          <MaterialIcons name="arrow-back" size={24} color={COLORS.text} />
        </TouchableOpacity>
      )
    });

    // Fetch subjects for the selected category
    fetchSubjectsForCategory(categoryId, categoryName);
  };

  // Function to reset to categories view
  const resetToCategoriesView = () => {
    console.log('Resetting to categories view');
    setSelectedCategory(null);
    setSubjects([]);
    navigation.setOptions({
      title: 'Exam Categories',
      headerLeft: undefined // Reset to default back button
    });
  };

  const handleSubjectPress = (subject) => {
    if (!selectedCategory) return;

    // Determine question count based on subject type
    let questionCount = 50; // Default for subject-wise tests
    let durationMinutes = subject.duration_minutes || 60;

    // Format duration for display
    let duration;
    if (durationMinutes >= 60) {
      const hours = Math.floor(durationMinutes / 60);
      const minutes = durationMinutes % 60;
      duration = hours + (minutes > 0 ? ` hour${hours > 1 ? 's' : ''} ${minutes} minutes` : ` hour${hours > 1 ? 's' : ''}`);
    } else {
      duration = `${durationMinutes} minutes`;
    }

    // Create a mock exam object to pass to TestInstructions
    const mockExam = {
      id: subject.id,
      name: `${selectedCategory.name} ${subject.name} Practice Test`,
      category: selectedCategory.id,
      categoryName: selectedCategory.name,
      description: subject.description || `Practice test for ${subject.name} in ${selectedCategory.name}`,
      duration: duration,
      duration_minutes: durationMinutes,
      questions: questionCount,
      price: 'Free',
      icon: subject.icon,
      isFullMock: subject.isFullMock,
      subjectId: subject.id
    };

    // Navigate to test instructions with the mock exam
    console.log('Navigating to TestInstructions with exam:', mockExam);
    navigation.navigate('TestInstructions', { exam: mockExam });
  };

  // We don't need these functions anymore as we're navigating directly to subject selection
  // Keeping the code commented for reference in case we need to revert
  /*
  const isExamSubscribed = (examId) => {
    return subscribedExams.some(exam => exam.id === examId);
  };

  const getExamScore = (examId) => {
    const exam = subscribedExams.find(exam => exam.id === examId);
    return exam ? `${exam.score}/${exam.totalQuestions}` : null;
  };

  const handleExamPress = (exam) => {
    const isSubscribed = isExamSubscribed(exam.id);

    if (isSubscribed) {
      // Navigate to exam questions - no limit on retakes for purchased tests
      navigation.navigate('ExamQuestionsScreen', {
        subject: exam.name,
        examId: exam.id,
        isRetake: true,
        category: exam.category,
        isFullMock: exam.isFullMock
      });
    } else {
      // Navigate to test instructions/payment for unsubscribed exams
      navigation.navigate('TestInstructions', {
        exam: exam
      });
    }
  };
  */

  const renderCategories = () => {
    console.log('Rendering categories, examCategories:', examCategories);
    return (
      <View style={styles.categoriesContainer}>
        <Text style={styles.sectionTitle}>Select an Exam Category</Text>
        <Text style={styles.sectionSubtitle}>Choose a category to view available subjects and tests</Text>

        {loadingCategories ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading exam categories...</Text>
          </View>
        ) : examCategories.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>No exam categories available.</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                console.log('Retry button pressed');
                loadExamCategories();
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.categoryGrid}>
            {examCategories.map((category) => (
              <CategoryCard
                key={category.id}
                title={category.name}
                price={category.name === 'NEET' ? '$10/month' :
                       category.name === 'JEE' ? '$14/month' :
                       category.name === 'GATE' ? '$16/month' : '$12/month'}
                rating={category.name === 'NEET' ? '4.9' :
                        category.name === 'JEE' ? '4.8' :
                        category.name === 'GATE' ? '4.7' : '4.5'}
                reviews={category.name === 'NEET' ? '689' :
                         category.name === 'JEE' ? '464' :
                         category.name === 'GATE' ? '325' : '250'}
                onPress={() => handleCategoryPress(category.id, category.name)}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  // SubjectCard component for grid layout
  const SubjectCard = ({ subject, onPress }) => {
    // Get icon based on subject name
    const getIconComponent = () => {
      const iconName = subject.icon || getIconForSubject(subject.name);

      // Determine background color based on subject
      let bgColor = '#F0F0F0';
      let iconColor = '#3461eb';

      if (subject.name.toLowerCase().includes('physics')) {
        bgColor = '#FFE6E6';
        iconColor = '#F44336';
      } else if (subject.name.toLowerCase().includes('chemistry')) {
        bgColor = '#E6F9F0';
        iconColor = '#4CAF50';
      } else if (subject.name.toLowerCase().includes('biology') || subject.name.toLowerCase().includes('botany')) {
        bgColor = '#E6F0FF';
        iconColor = '#2196F3';
      } else if (subject.name.toLowerCase().includes('mathematics') || subject.name.toLowerCase().includes('math')) {
        bgColor = '#FFF9E6';
        iconColor = '#FF9500';
      } else if (subject.name.toLowerCase().includes('full mock')) {
        bgColor = '#E6E6FF';
        iconColor = '#673AB7';
      }

      return (
        <View style={[styles.categoryIcon, { backgroundColor: bgColor }]}>
          <MaterialCommunityIcons name={iconName} size={32} color={iconColor} />
        </View>
      );
    };

    // Generate mock price based on subject name
    const getPrice = () => {
      if (subject.name.toLowerCase().includes('physics')) return '$10/month';
      if (subject.name.toLowerCase().includes('chemistry')) return '$14/month';
      if (subject.name.toLowerCase().includes('biology')) return '$16/month';
      if (subject.name.toLowerCase().includes('mathematics')) return '$12/month';
      if (subject.name.toLowerCase().includes('full mock')) return '$20/month';
      return '$15/month';
    };

    // Generate mock rating based on subject name
    const getRating = () => {
      if (subject.name.toLowerCase().includes('physics')) return '4.7';
      if (subject.name.toLowerCase().includes('chemistry')) return '4.8';
      if (subject.name.toLowerCase().includes('biology')) return '4.9';
      if (subject.name.toLowerCase().includes('mathematics')) return '4.6';
      if (subject.name.toLowerCase().includes('full mock')) return '4.9';
      return '4.5';
    };

    // Generate mock review count
    const getReviews = () => {
      if (subject.name.toLowerCase().includes('physics')) return '425';
      if (subject.name.toLowerCase().includes('chemistry')) return '464';
      if (subject.name.toLowerCase().includes('biology')) return '389';
      if (subject.name.toLowerCase().includes('mathematics')) return '512';
      if (subject.name.toLowerCase().includes('full mock')) return '325';
      return '250';
    };

    return (
      <TouchableOpacity style={styles.categoryCard} onPress={onPress} activeOpacity={0.7}>
        {getIconComponent()}
        <Text style={styles.categoryTitle}>{subject.name}</Text>
        <Text style={styles.categoryPrice}>{getPrice()}</Text>
        <View style={styles.ratingContainer}>
          <MaterialIcons name="star" size={14} color="#FFC107" />
          <Text style={styles.ratingText}>{getRating()}</Text>
          <Text style={styles.reviewsText}>({getReviews()} reviews)</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSubjects = () => {
    if (!selectedCategory) return null;

    return (
      <View style={styles.subjectsContainer}>
        <Text style={styles.sectionTitle}>{selectedCategory.name}</Text>
        <Text style={styles.sectionSubtitle}>Select a subject to take a practice test</Text>

        {loadingSubjects ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading subjects...</Text>
          </View>
        ) : subjects.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>No subjects available for this category.</Text>
          </View>
        ) : (
          <View style={styles.categoryGrid}>
            {subjects.map((subject, index) => (
              <SubjectCard
                key={subject.id || index}
                subject={subject}
                onPress={() => handleSubjectPress(subject)}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  console.log('Rendering FullExamListScreen');

  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading exams...</Text>
        </View>
      ) : (
        <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
          {!selectedCategory ? (
            // Show categories
            <View style={{ flex: 1 }}>
              {/* Header is now provided by Tab.Screen options */}
              <ScrollView
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[COLORS.primary]}
                    tintColor={COLORS.primary}
                  />
                }
              >
                {renderCategories()}
              </ScrollView>
            </View>
          ) : (
            // Show subjects for selected category
            <View style={{ flex: 1 }}>
              <ScrollView
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
              >
                {renderSubjects()}
              </ScrollView>
            </View>
          )}
        </Animated.View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.textSecondary,
    ...FONTS.medium,
  },
  header: {
    backgroundColor: COLORS.background,
    padding: 16,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    color: COLORS.text,
    fontSize: 18,
    ...FONTS.bold,
    flex: 1,
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  headerWithBack: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: 8,
    marginBottom: 16,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  // Grid layout styles
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  categoryPrice: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.text,
    marginLeft: 4,
  },
  reviewsText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  // Old category styles (keeping for reference)
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  sectionTitle: {
    fontSize: 22,
    color: COLORS.text,
    ...FONTS.bold,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    ...FONTS.regular,
    marginBottom: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  examCard: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  examCardContent: {
    flexDirection: 'row',
    padding: 16,
  },
  examIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  examDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  examName: {
    fontSize: 16,
    color: COLORS.text,
    ...FONTS.medium,
    marginBottom: 4,
  },
  examDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    ...FONTS.regular,
    marginBottom: 8,
  },
  examMetaContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  examMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  examMetaText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    ...FONTS.regular,
    marginLeft: 4,
  },
  scoreText: {
    fontSize: 13,
    color: COLORS.primary,
    ...FONTS.medium,
    marginTop: 4,
  },
  examActionContainer: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: COLORS.success,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#FFFFFF',
    ...FONTS.medium,
  },
  priceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: COLORS.border,
  },
  priceText: {
    fontSize: 14,
    color: COLORS.text,
    ...FONTS.bold,
  },
  emptyStateContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#eee',
    marginHorizontal: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  subjectsContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  subjectsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  subjectsTitle: {
    fontSize: 18,
    ...FONTS.bold,
    color: COLORS.text,
    flex: 1,
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  placeholder: {
    width: 24,
  },
  // We're now using the categoryCard styles for subjects too
  subjectButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  subjectButton: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  subjectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default FullExamListScreen;