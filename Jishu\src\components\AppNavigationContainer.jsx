import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { logoutUser } from '../store/slices/authSlice';
import Header from '../screens/Header';

const AppNavigationContainer = ({ children }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const handleLogout = () => {
    dispatch(logoutUser());
  };

  return (
    <Header
      userName={user?.name}
      userImage={user?.avatar}
      onLogout={handleLogout}
    >
      {children}
    </Header>
  );
};



export default AppNavigationContainer;
