# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
generate_cxx_metadata completed in 24ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
generate_cxx_metadata completed in 16ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 16ms
  [gap of 10ms]
generate_cxx_metadata completed in 42ms

# C/C++ build system timings
generate_cxx_metadata 27ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 25ms
  [gap of 10ms]
generate_cxx_metadata completed in 60ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 40ms]
  create-invalidation-state 51ms
  [gap of 53ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 164ms

