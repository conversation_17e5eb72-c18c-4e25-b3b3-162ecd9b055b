from app import db
from datetime import datetime

class Notification(db.Model):
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # exam_result, payment, community, system
    related_id = db.Column(db.Integer)  # ID of related entity (exam_attempt_id, payment_id, post_id, etc.)
    is_read = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('notifications', lazy='dynamic'))
    
    __table_args__ = (
        db.<PERSON>('idx_user_id', 'user_id'),
        db.Index('idx_is_read', 'is_read'),
    )
    
    def __repr__(self):
        return f'<Notification {self.id} User:{self.user_id} Type:{self.notification_type}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'message': self.message,
            'notification_type': self.notification_type,
            'related_id': self.related_id,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
