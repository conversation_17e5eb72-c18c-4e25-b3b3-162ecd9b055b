{"name": "Jishu", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/app": "^21.10.0", "@react-native-firebase/auth": "^21.10.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "@reduxjs/toolkit": "^2.7.0", "node-fetch": "^2.7.0", "react": "18.3.1", "react-native": "0.77.1", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.23.1", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-paper": "^5.13.1", "react-native-reanimated": "^3.16.7", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.6.0", "react-native-snackbar": "^2.8.0", "react-native-svg": "^15.11.1", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "^0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}