"""
Migration to alter questions_with_options table
"""

from app import create_app, db

def upgrade():
    """Add new columns to questions_with_options table"""
    app = create_app()
    with app.app_context():
        # Add new columns
        db.engine.execute('''
        ALTER TABLE questions_with_options
        ADD COLUMN user_id INT,
        ADD COLUMN exam_category_id INT,
        ADD COLUMN is_ai_generated BOOLEAN DEFAULT FALSE,
        ADD FOREIGN KEY (user_id) REFERENCES users(id),
        ADD FOREIGN KEY (exam_category_id) REFERENCES exam_categories(id),
        ADD INDEX idx_is_ai_generated (is_ai_generated)
        ''')

if __name__ == "__main__":
    upgrade()