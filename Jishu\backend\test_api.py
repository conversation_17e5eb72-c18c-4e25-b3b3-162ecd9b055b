from flask import Flask, jsonify, request
import os

app = Flask(__name__)

@app.route('/test', methods=['GET'])
def test():
    return jsonify({'message': 'API is working!'})

@app.route('/test-post', methods=['POST'])
def test_post():
    data = request.get_json() or {}
    return jsonify({
        'message': 'POST request received!',
        'data': data
    })

if __name__ == '__main__':
    # Get the IP address from the environment variable or use 0.0.0.0 (all interfaces)
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 5000))
    
    print(f"Starting test API server on {host}:{port}")
    print(f"Test GET endpoint: http://{host}:{port}/test")
    print(f"Test POST endpoint: http://{host}:{port}/test-post")
    
    app.run(host=host, port=port, debug=True)
