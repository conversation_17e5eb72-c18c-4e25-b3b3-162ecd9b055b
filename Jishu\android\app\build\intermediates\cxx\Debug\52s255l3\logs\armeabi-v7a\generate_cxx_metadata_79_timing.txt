# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 25ms
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 39ms
  [gap of 14ms]
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 28ms
  [gap of 11ms]
generate_cxx_metadata completed in 56ms

