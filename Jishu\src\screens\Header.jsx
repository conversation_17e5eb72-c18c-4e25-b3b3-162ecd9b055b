import React, { useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Linking,
  Platform,
  StatusBar,
  DrawerLayoutAndroid,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
// Import icons from react-native-vector-icons
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { logoutUser } from '../store/slices/authSlice';
import { useTheme } from '../context/ThemeContext';

// Helper function to get the correct avatar source
const getAvatarSource = (avatarPath) => {
  if (!avatarPath) {
    return require('../../assets/images/index.png'); // Default image
  }

  if (typeof avatarPath === 'string') {
    if (avatarPath.startsWith('http')) {
      return { uri: avatarPath }; // Remote URL
    }

    // For local images, use a switch statement to map filenames to require statements
    switch (avatarPath) {
      case 'engg1.jpg':
        return require('../../assets/images/engg1.jpg');
      case 'engg2.jpg':
        return require('../../assets/images/engg2.jpg');
      case 'doc.jpg':
        return require('../../assets/images/doc.jpg');
      case 'index.png':
      default:
        return require('../../assets/images/index.png');
    }
  }

  // If it's already a require() result
  return avatarPath;
};

const Header = ({ userName, userImage, onLogout, children }) => {
  // Redux setup
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const navigation = useNavigation();

  // Theme context
  const { theme, isDarkMode, toggleTheme } = useTheme();

  // Drawer reference
  const drawer = useRef(null);

  // Use provided props or fall back to Redux state
  const displayName = userName || user?.name || 'Guest';
  const displayImage = userImage || user?.avatar;

  // Handle social media links
  const openSocialMedia = (platform) => {
    let url = '';
    switch (platform) {
      case 'whatsapp':
        url = 'https://wa.me/919876543210'; // Replace with your WhatsApp number
        break;
      case 'facebook':
        url = 'https://www.facebook.com/jishuapp'; // Replace with your Facebook page
        break;
      case 'instagram':
        url = 'https://www.instagram.com/jishuapp'; // Replace with your Instagram page
        break;
      default:
        return;
    }

    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      } else {
        console.log(`Cannot open URL: ${url}`);
      }
    });
  };

  // Handle navigation
  const navigateTo = (screen) => {
    drawer.current?.closeDrawer();
    if (screen === 'logout') {
      // Show confirmation dialog before logout
      Alert.alert(
        'Logout',
        'Are you sure you want to logout?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Logout',
            onPress: () => {
              if (onLogout) {
                onLogout();
              } else {
                dispatch(logoutUser());
              }
            },
            style: 'destructive'
          },
        ]
      );
    } else {
      navigation.navigate(screen);
    }
  };

  // Render the navigation drawer content
  const renderDrawerContent = () => (
    <ScrollView style={[styles.drawerContainer, { backgroundColor: theme.card }]}>
      {/* User Profile Section */}
      <View style={[styles.profileContainer, { borderBottomColor: theme.border }]}>
        <Image
          source={getAvatarSource(displayImage)}
          style={[styles.profileImage, { borderColor: theme.primary }]}
        />
        <Text style={[styles.profileName, { color: theme.text }]}>{displayName}</Text>
        {user?.role && <Text style={[styles.roleText, { color: theme.primary }]}>{user.role}</Text>}
        <TouchableOpacity
          style={[styles.editProfileButton, {
            backgroundColor: isDarkMode ? theme.darkGray : theme.lightGray,
            borderColor: theme.primary
          }]}
          onPress={() => navigateTo('Profile')}
          activeOpacity={0.7}
        >
          <Text style={[styles.editProfileText, { color: theme.primary }]}>Edit Profile</Text>
        </TouchableOpacity>
      </View>

      {/* Menu Items */}
      <View style={styles.menuItemsContainer}>
        {/* App Navigation */}
        <View style={styles.menuSection}>
          <Text style={[styles.menuSectionTitle, { color: theme.textSecondary }]}>Navigation</Text>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: theme.border }]}
            onPress={() => navigateTo('MainTabs')}
            activeOpacity={0.7}
          >
            <Ionicons name="home-outline" size={22} color={theme.text} />
            <Text style={[styles.menuText, { color: theme.text }]}>Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: theme.border }]}
            onPress={() => navigateTo('Achivements')}
            activeOpacity={0.7}
          >
            <Ionicons name="trophy-outline" size={22} color={theme.text} />
            <Text style={[styles.menuText, { color: theme.text }]}>Achievements</Text>
          </TouchableOpacity>
        </View>

        {/* Social Media Links */}
        <View style={styles.menuSection}>
          <Text style={[styles.menuSectionTitle, { color: theme.textSecondary }]}>Connect With Us</Text>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: theme.border }]}
            onPress={() => openSocialMedia('whatsapp')}
            activeOpacity={0.7}
          >
            <FontAwesome name="whatsapp" size={22} color="#25D366" />
            <Text style={[styles.menuText, { color: theme.text }]}>WhatsApp</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: theme.border }]}
            onPress={() => openSocialMedia('facebook')}
            activeOpacity={0.7}
          >
            <FontAwesome name="facebook-square" size={22} color="#3b5998" />
            <Text style={[styles.menuText, { color: theme.text }]}>Facebook</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: theme.border }]}
            onPress={() => openSocialMedia('instagram')}
            activeOpacity={0.7}
          >
            <FontAwesome name="instagram" size={22} color="#C13584" />
            <Text style={[styles.menuText, { color: theme.text }]}>Instagram</Text>
          </TouchableOpacity>
        </View>

        {/* Settings & Support */}
        <View style={styles.menuSection}>
          <Text style={[styles.menuSectionTitle, { color: theme.textSecondary }]}>Settings & Support</Text>

          {/* Theme Toggle */}
          <View style={styles.menuItem}>
            <MaterialIcons
              name={isDarkMode ? "dark-mode" : "light-mode"}
              size={22}
              color={isDarkMode ? "#9BA4B5" : "#FDB813"}
            />
            <Text style={styles.menuText}>{isDarkMode ? 'Dark Mode' : 'Light Mode'}</Text>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: '#767577', true: '#3461eb' }}
              thumbColor={isDarkMode ? '#f5f5f5' : '#f4f3f4'}
              ios_backgroundColor="#767577"
            />
          </View>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateTo('Settings')}
            activeOpacity={0.7}
          >
            <Ionicons name="settings-outline" size={22} color={theme.text} />
            <Text style={[styles.menuText, { color: theme.text }]}>Settings</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateTo('Privacy')}
            activeOpacity={0.7}
          >
            <MaterialIcons name="privacy-tip" size={22} color={theme.text} />
            <Text style={[styles.menuText, { color: theme.text }]}>Privacy & Security</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateTo('Support')}
            activeOpacity={0.7}
          >
            <MaterialIcons name="help-outline" size={22} color={theme.text} />
            <Text style={[styles.menuText, { color: theme.text }]}>Help & Support</Text>
          </TouchableOpacity>

          {/* Connection Test - For debugging */}
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateTo('ConnectionTest')}
            activeOpacity={0.7}
          >
            <MaterialIcons name="wifi" size={22} color={theme.text} />
            <Text style={[styles.menuText, { color: theme.text }]}>Connection Test</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Logout Button */}
      <TouchableOpacity
        style={[styles.logoutButton, {
          backgroundColor: theme.danger,
          shadowColor: theme.danger
        }]}
        onPress={() => navigateTo('logout')}
        activeOpacity={0.7}
      >
        <FontAwesome name="sign-out" size={22} color="#fff" />
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>

      {/* App Version */}
      <Text style={[styles.versionText, { color: theme.textSecondary }]}>Version 1.0.0</Text>
    </ScrollView>
  );

  // Render the main component
  if (Platform.OS === 'android') {
    return (
      <DrawerLayoutAndroid
        ref={drawer}
        drawerWidth={300}
        drawerPosition="left"
        renderNavigationView={renderDrawerContent}
        drawerBackgroundColor={theme.card}
      >
        <View style={{ flex: 1 }}>
          <StatusBar
            backgroundColor={theme.background}
            barStyle={isDarkMode ? "light-content" : "dark-content"}
          />
          <View style={[styles.headerContainer, {
            backgroundColor: theme.background,
            borderBottomColor: theme.border
          }]}>
            {/* Hamburger Menu Icon */}
            <TouchableOpacity
              onPress={() => drawer.current?.openDrawer()}
              style={styles.menuButton}
              activeOpacity={0.7}
            >
              <MaterialIcons name="menu" size={28} color={theme.primary} />
            </TouchableOpacity>

            {/* User Info */}
            <View style={styles.userInfo}>
              <Text style={[styles.userName, { color: theme.text }]}>{displayName}</Text>
            </View>

            {/* User Avatar */}
            <TouchableOpacity
              onPress={() => drawer.current?.openDrawer()}
              activeOpacity={0.7}
            >
              <Image
                source={getAvatarSource(displayImage)}
                style={[styles.headerAvatar, { borderColor: theme.primary }]}
              />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={{ flex: 1 }}>
            {children}
          </View>
        </View>
      </DrawerLayoutAndroid>
    );
  } else {
    // For iOS, we'll use a custom drawer implementation
    // This is a simplified version for this example
    return (
      <View style={{ flex: 1 }}>
        <StatusBar
          backgroundColor={theme.background}
          barStyle={isDarkMode ? "light-content" : "dark-content"}
        />
        <View style={[styles.headerContainer, {
          backgroundColor: theme.background,
          borderBottomColor: theme.border
        }]}>
          {/* Hamburger Menu Icon */}
          <TouchableOpacity
            onPress={() => alert('iOS drawer not implemented in this example')}
            style={styles.menuButton}
            activeOpacity={0.7}
          >
            <MaterialIcons name="menu" size={28} color={theme.primary} />
          </TouchableOpacity>

          {/* User Info */}
          <View style={styles.userInfo}>
            <Text style={[styles.userName, { color: theme.text }]}>{displayName}</Text>
          </View>

          {/* User Avatar */}
          <TouchableOpacity
            onPress={() => alert('iOS drawer not implemented in this example')}
            activeOpacity={0.7}
          >
            <Image
              source={getAvatarSource(displayImage)}
              style={[styles.headerAvatar, { borderColor: theme.primary }]}
            />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View style={{ flex: 1 }}>
          {children}
        </View>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 10,
  },
  menuButton: {
    padding: 8,
    borderRadius: 20,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    marginLeft: 10,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#3461eb',
  },
  drawerContainer: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileImage: {
    width: 90,
    height: 90,
    borderRadius: 45,
    marginBottom: 12,
    borderWidth: 3,
    borderColor: '#3461eb',
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  roleText: {
    fontSize: 14,
    color: '#3461eb',
    marginBottom: 12,
  },
  editProfileButton: {
    backgroundColor: '#f0f4ff',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#3461eb',
  },
  editProfileText: {
    color: '#3461eb',
    fontSize: 14,
    fontWeight: '500',
  },
  menuItemsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  menuSection: {
    marginBottom: 24,
  },
  menuSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#888',
    marginBottom: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuText: {
    fontSize: 16,
    marginLeft: 16,
    color: '#333',
    flex: 1,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#d9534f',
    marginHorizontal: 20,
    marginBottom: 16,
    marginTop: 20,
    paddingVertical: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#d9534f',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 10,
  },
  versionText: {
    textAlign: 'center',
    color: '#999',
    fontSize: 12,
    marginBottom: 20,
    marginTop: 10,
  },
});

export default Header;
