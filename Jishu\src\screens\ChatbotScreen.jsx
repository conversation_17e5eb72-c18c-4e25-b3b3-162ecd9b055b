import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  Animated,
  SafeAreaView,
  StatusBar,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { aiAPI, chatAPI } from '../services/api';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336',
  // Chat colors
  chatBackground: '#F9FAFA',
  userBubble: '#3461eb',  // Primary color
  botBubble: '#FFFFFF',
  headerColor: '#3461eb',  // Primary color
  inputBar: '#F0F0F0'
};

const ChatbotScreen = ({ route }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Get parameters from route
  const params = route?.params || {};
  const isPersonalChat = params.isPersonalChat || false;
  const userId = params.userId;
  const userName = params.userName || 'User';
  const userAvatar = params.userAvatar || 'https://randomuser.me/api/portraits/men/32.jpg';

  // Animation value for content fade
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Reference to FlatList to scroll to bottom
  const flatListRef = useRef(null);

  // Sample bot avatar
  const botAvatar = 'https://randomuser.me/api/portraits/lego/1.jpg';

  // Load initial messages
  useEffect(() => {
    if (isPersonalChat && userId) {
      // Load personal chat messages
      loadPersonalChatMessages();
    } else {
      // Show AI greeting for regular chatbot
      setTimeout(() => {
        const greeting = {
          id: 1,
          text: "Hello! I'm your AI Master. How can I help you with your exam preparation today? I can answer questions, provide study tips, or help you prepare for specific subjects.",
          isUser: false,
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
        setMessages([greeting]);
      }, 500);
    }
  }, [isPersonalChat, userId]);

  // Function to load personal chat messages
  const loadPersonalChatMessages = async () => {
    if (!userId) return;

    setIsTyping(true);

    try {
      const response = await chatAPI.getPersonalChatMessages(userId);

      if (Array.isArray(response)) {
        // If the response is empty, show a welcome message
        if (response.length === 0) {
          const welcomeMessage = {
            id: Date.now(),
            text: `Hello! I'm ${userName}. How can I help you today?`,
            isUser: false,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            avatar: userAvatar
          };

          setMessages([welcomeMessage]);
          return;
        }

        // Convert API messages to our format
        const formattedMessages = response.map(msg => ({
          id: msg.id,
          text: msg.message,
          isUser: msg.user_id !== userId, // If the message is from the current user, it's not from the other person
          timestamp: new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          avatar: msg.user_avatar
        })).reverse(); // Reverse to show newest at the bottom

        setMessages(formattedMessages);
      } else {
        // If the response is not an array, show a fallback message
        const fallbackMessage = {
          id: Date.now(),
          text: `Hello! I'm ${userName}. How can I help you today?`,
          isUser: false,
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          avatar: userAvatar
        };

        setMessages([fallbackMessage]);
      }
    } catch (error) {
      console.error('Error loading personal chat messages:', error);

      // Show a fallback message
      const fallbackMessage = {
        id: Date.now(),
        text: `Hello! I'm ${userName}. How can I help you today?`,
        isUser: false,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        avatar: userAvatar
      };

      setMessages([fallbackMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // Keyboard listeners for chat scrolling
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (e) => {
        setKeyboardVisible(true);

        // Scroll to bottom when keyboard appears
        setTimeout(() => scrollToBottom(), 100);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Scroll to bottom of chat
  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToOffset({ offset: 0, animated: true });
    }
  };

  // Format chat history for API
  const formatChatHistoryForAPI = () => {
    return messages.map(msg => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.text
    })).reverse(); // Reverse to get chronological order
  };

  const handleSend = async () => {
    if (inputText.trim()) {
      // Add user message to the chat
      const userMessage = {
        id: Date.now(),
        text: inputText,
        isUser: true,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      // Update messages and clear input
      setMessages(prevMessages => [userMessage, ...prevMessages]);
      setInputText('');

      // Show typing indicator
      setIsTyping(true);

      try {
        if (isPersonalChat && userId) {
          // Send message to personal chat API
          const response = await chatAPI.sendPersonalChatMessage(userId, userMessage.text);

          // Check if we got a response message
          if (response && response.response_message) {
            const responseMsg = response.response_message;

            // Create response message
            const responseMessage = {
              id: responseMsg.id || Date.now() + 1,
              text: responseMsg.message,
              isUser: false,
              timestamp: new Date(responseMsg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) ||
                        new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
              avatar: userAvatar
            };

            // Hide typing indicator and add response message
            setIsTyping(false);
            setMessages(prevMessages => [responseMessage, ...prevMessages]);
          } else {
            // If no response message in the API response, create a generic one
            const fallbackMessage = {
              id: Date.now() + 1,
              text: `Thanks for your message! I'll get back to you soon.`,
              isUser: false,
              timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            };

            setIsTyping(false);
            setMessages(prevMessages => [fallbackMessage, ...prevMessages]);
          }
        } else {
          // Regular AI chatbot flow
          // Get chat history in the correct format for the API
          const chatHistory = formatChatHistoryForAPI();

          // Send message to AI API
          const response = await aiAPI.sendMessage(userMessage.text, chatHistory);

          // Create AI message from response
          const aiMessage = {
            id: Date.now() + 1,
            text: response.response || "I'm sorry, I couldn't process your request at the moment.",
            isUser: false,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          };

          // Hide typing indicator and add AI message
          setIsTyping(false);
          setMessages(prevMessages => [aiMessage, ...prevMessages]);
        }
      } catch (error) {
        console.error('Error getting response:', error);

        // Hide typing indicator
        setIsTyping(false);

        if (isPersonalChat) {
          // Fallback for personal chat
          const fallbackMessage = {
            id: Date.now() + 1,
            text: `Sorry, I couldn't send your message due to a network issue. Please try again later.`,
            isUser: false,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          };

          setMessages(prevMessages => [fallbackMessage, ...prevMessages]);
        } else {
          // Fallback for AI chat
          const fallbackResponse = generateFallbackResponse(userMessage.text);
          const aiMessage = {
            id: Date.now() + 1,
            text: fallbackResponse,
            isUser: false,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          };

          setMessages(prevMessages => [aiMessage, ...prevMessages]);
        }
      }
    }
  };

  // Generate fallback responses when API fails
  const generateFallbackResponse = (userInput) => {
    const input = userInput.toLowerCase();

    // Exam preparation related responses
    if (input.includes('study') || input.includes('prepare')) {
      return "To prepare effectively, create a study schedule, use active recall techniques, and take regular practice tests. Would you like me to help you create a study plan?";
    }
    else if (input.includes('neet') || input.includes('medical')) {
      return "For NEET preparation, focus on NCERT books first, then move to practice questions. Biology carries the most weight, so ensure you're thorough with diagrams and concepts. Would you like specific topic recommendations?";
    }
    else if (input.includes('jee') || input.includes('engineering')) {
      return "For JEE preparation, build a strong foundation in Physics, Chemistry, and Mathematics. Practice numerical problems daily and focus on understanding concepts rather than memorizing. Which subject do you find most challenging?";
    }
    else if (input.includes('mock') || input.includes('test') || input.includes('practice')) {
      return "Taking regular mock tests is crucial for exam success. It helps you manage time better and identifies weak areas. Our app offers subject-wise practice tests with detailed analytics. Would you like to try one now?";
    }
    else if (input.includes('difficult') || input.includes('hard') || input.includes('struggling')) {
      return "It's normal to find certain topics challenging. Let's break it down into smaller parts and tackle them one by one. Which specific concept are you struggling with?";
    }
    else if (input.includes('thank')) {
      return "You're welcome! I'm here to help with your exam preparation anytime. Is there anything else you'd like to know?";
    }
    else if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
      return "Hello! How can I assist with your exam preparation today?";
    }
    else {
      return "I understand you're asking about '" + userInput + "'. Could you provide more details so I can give you specific guidance for your exam preparation?";
    }
  };

  const renderMessage = ({ item }) => {
    // Don't render anything for empty items
    if (!item) return null;

    // Determine which avatar to show
    const avatarUri = isPersonalChat
      ? (item.avatar || userAvatar)
      : botAvatar;

    return (
      <View style={[styles.messageRow, item.isUser ? styles.userMessageRow : styles.botMessageRow]}>
        {!item.isUser && (
          <Image
            source={{ uri: avatarUri }}
            style={styles.avatar}
            resizeMode="cover"
          />
        )}

        <View style={[styles.messageBubble, item.isUser ? styles.userBubble : styles.botBubble]}>
          <Text style={[styles.messageText, item.isUser ? styles.userMessageText : styles.botMessageText]}>
            {item.text}
          </Text>
          <Text style={[
            styles.timestamp,
            item.isUser ? { color: 'rgba(255, 255, 255, 0.7)' } : { color: COLORS.textSecondary }
          ]}>
            {item.timestamp}
          </Text>
        </View>

        {item.isUser && <View style={styles.spacer} />}
      </View>
    );
  };

  // Render typing indicator
  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    // Determine which avatar to show
    const avatarUri = isPersonalChat ? userAvatar : botAvatar;

    return (
      <View style={[styles.messageRow, styles.botMessageRow]}>
        <Image
          source={{ uri: avatarUri }}
          style={styles.avatar}
          resizeMode="cover"
        />
        <View style={[styles.messageBubble, styles.botBubble, styles.typingBubble]}>
          <View style={styles.typingIndicator}>
            <View style={[styles.typingDot, styles.typingDot1]} />
            <View style={[styles.typingDot, styles.typingDot2]} />
            <View style={[styles.typingDot, styles.typingDot3]} />
          </View>
        </View>
      </View>
    );
  };

  // We don't need to animate the input container anymore
  // since we want it to stay fixed at the bottom

  // Add bottom padding to account for the bottom navigation bar
  const bottomNavHeight = 0; // Set to 0 to eliminate gap

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={{ uri: isPersonalChat ? userAvatar : botAvatar }}
            style={styles.headerAvatar}
          />
          <View>
            <Text style={styles.headerTitle}>
              {isPersonalChat ? userName : 'AI Master'}
            </Text>
            <Text style={styles.headerSubtitle}>
              {isTyping ? 'typing...' : 'online'}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.keyboardAvoidingView}>
        {/* Chat History */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.chatContainer}
          inverted // Start from the bottom
          ListHeaderComponent={renderTypingIndicator}
          showsVerticalScrollIndicator={false}
        />

        {/* Input Area - Fixed at bottom above nav bar */}
        <View style={[styles.inputContainer, { bottom: bottomNavHeight }]}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.input}
              placeholder="Type a message"
              placeholderTextColor={COLORS.textSecondary}
              value={inputText}
              onChangeText={setInputText}
              multiline
            />
          </View>

          <TouchableOpacity
            style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
            onPress={handleSend}
            disabled={!inputText.trim()}
          >
            <MaterialIcons
              name="send"
              size={24}
              color="white"
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // Center the header content
    padding: 14,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
  headerRight: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
  },
  chatContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 80, // Space for input container only
  },
  messageRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-end',
    width: '100%',
  },
  userMessageRow: {
    justifyContent: 'flex-end',
    paddingLeft: 40, // Give more space on the left for user messages
  },
  botMessageRow: {
    justifyContent: 'flex-start',
    paddingRight: 40, // Give more space on the right for bot messages
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  spacer: {
    width: 38,
    marginLeft: 8,
  },
  messageBubble: {
    maxWidth: '75%',
    padding: 12,
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    marginHorizontal: 4,
  },
  userBubble: {
    backgroundColor: COLORS.primary,
    borderBottomRightRadius: 4,
    marginLeft: 'auto', // Push to the right side
  },
  botBubble: {
    backgroundColor: COLORS.card,
    borderBottomLeftRadius: 4,
    marginRight: 'auto', // Push to the left side
  },
  messageText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 6,
    letterSpacing: 0.2,
  },
  userMessageText: {
    color: 'white',
    fontWeight: '500',
  },
  botMessageText: {
    color: COLORS.text,
    fontWeight: '400',
  },
  timestamp: {
    fontSize: 10,
    color: COLORS.textSecondary,
    alignSelf: 'flex-end',
    marginTop: 2,
    opacity: 0.8,
  },
  typingBubble: {
    paddingVertical: 14,
    paddingHorizontal: 18,
    width: 80,
    minHeight: 40,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingDot: {
    width: 9,
    height: 9,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
    marginHorizontal: 3,
  },
  typingDot1: {
    opacity: 0.4,
    transform: [{ scale: 0.8 }],
    backgroundColor: COLORS.primary,
  },
  typingDot2: {
    opacity: 0.7,
    transform: [{ scale: 1 }],
    backgroundColor: COLORS.primary,
  },
  typingDot3: {
    opacity: 0.4,
    transform: [{ scale: 0.8 }],
    backgroundColor: COLORS.primary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingHorizontal: 16,
    backgroundColor: COLORS.card,
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 1000,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 24,
    paddingHorizontal: 16,
    marginRight: 10,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  input: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingVertical: 10,
    color: COLORS.text,
    letterSpacing: 0.2,
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  sendButtonDisabled: {
    backgroundColor: COLORS.border,
    opacity: 0.7,
  },
});

export default ChatbotScreen;