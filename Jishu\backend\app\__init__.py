import pymysql
pymysql.install_as_MySQLdb()

from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from config import Config
from datetime import datetime

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Set JWT header type to JWT instead of Bearer
    app.config['JWT_HEADER_TYPE'] = 'JWT'

    # Set JWT identity claim key to 'sub'
    app.config['JWT_IDENTITY_CLAIM'] = 'sub'

    # Additional JWT configuration
    app.config['JWT_ERROR_MESSAGE_KEY'] = 'msg'

    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)

    # Configure CORS
    CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True, allow_headers=["Content-Type", "Authorization"])

    # Register blueprints
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    # Register auth blueprint with /api/auth prefix to match frontend expectations
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/api/auth')

    # Register AI blueprint
    from app.ai import ai_bp
    app.register_blueprint(ai_bp, url_prefix='/api/ai')

    @app.route('/health')
    def health_check():
        return {'status': 'healthy'}

    return app

from app import models

