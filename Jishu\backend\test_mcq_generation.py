#!/usr/bin/env python3
"""
Test script for MCQ generation endpoints using Ollama
"""

import requests
import json
import sys

# Base URL for the API
BASE_URL = "http://localhost:5000"

def test_ai_status():
    """Test the AI status endpoint"""
    print("Testing AI status endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/ai/status")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing AI status: {e}")
        return False

def test_generate_mcq_ollama():
    """Test the Ollama MCQ generation endpoint"""
    print("\nTesting Ollama MCQ generation endpoint...")
    
    # Test data
    test_data = {
        "topic": "photosynthesis",
        "subject": "Biology",
        "num_questions": 3,
        "use_pdf_content": False  # Use general knowledge for testing
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/ai/generate-mcq-ollama",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing Ollama MCQ generation: {e}")
        return False

def test_generate_mcq_from_pdfs():
    """Test the PDF-based MCQ generation endpoint"""
    print("\nTesting PDF-based MCQ generation endpoint...")
    
    try:
        # Test with GET request first
        response = requests.get(f"{BASE_URL}/ai/generate-mcq-from-pdfs?num_questions=2&topic=science")
        print(f"GET Status Code: {response.status_code}")
        print(f"GET Response: {json.dumps(response.json(), indent=2)}")
        
        # Test with POST request
        test_data = {
            "topic": "mathematics",
            "subject": "Math",
            "num_questions": 2
        }
        
        response = requests.post(
            f"{BASE_URL}/ai/generate-mcq-from-pdfs",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"POST Status Code: {response.status_code}")
        print(f"POST Response: {json.dumps(response.json(), indent=2)}")
        
        return response.status_code in [200, 400]  # 400 is expected if no PDFs found
    except Exception as e:
        print(f"Error testing PDF MCQ generation: {e}")
        return False

def main():
    """Run all tests"""
    print("Starting MCQ Generation API Tests")
    print("=" * 50)
    
    tests = [
        ("AI Status", test_ai_status),
        ("Ollama MCQ Generation", test_generate_mcq_ollama),
        ("PDF MCQ Generation", test_generate_mcq_from_pdfs)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        success = test_func()
        results.append((test_name, success))
        print(f"Result: {'PASS' if success else 'FAIL'}")
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"  {test_name}: {status}")
    
    # Overall result
    all_passed = all(success for _, success in results)
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
