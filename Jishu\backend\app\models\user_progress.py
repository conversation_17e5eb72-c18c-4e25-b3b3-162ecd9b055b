from app import db
from datetime import datetime, timezone

class ExamAttempt(db.Model):
    __tablename__ = 'exam_attempts'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON><PERSON>('users.id'), nullable=False)
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchases.id'), nullable=False)
    attempt_number = db.Column(db.Integer, default=1)  # Which attempt number this is for the user
    score = db.Column(db.Integer, default=0)
    total_questions = db.Column(db.Integer, default=0)
    correct_answers = db.Column(db.Integer, default=0)
    wrong_answers = db.Column(db.Integer, default=0)
    unattempted = db.Column(db.Integer, default=0)
    time_taken_seconds = db.Column(db.Integer)
    started_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    completed_at = db.Column(db.DateTime)

    user = db.relationship('User', backref=db.backref('attempts', lazy='dynamic'))
    purchase = db.relationship('Purchase', backref=db.backref('attempts', lazy='dynamic'))
    answers = db.relationship('UserAnswer', back_populates='attempt', lazy='dynamic', cascade='all, delete-orphan')

    __table_args__ = (
        db.Index('idx_exam_attempts_user_id', 'user_id'),
        db.Index('idx_exam_attempts_purchase_id', 'purchase_id'),
    )

    def __repr__(self):
        return f'<ExamAttempt {self.id} User:{self.user_id} Score:{self.score}/{self.total_questions}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'purchase_id': self.purchase_id,
            'attempt_number': self.attempt_number,
            'score': self.score,
            'total_questions': self.total_questions,
            'correct_answers': self.correct_answers,
            'wrong_answers': self.wrong_answers,
            'unattempted': self.unattempted,
            'time_taken_seconds': self.time_taken_seconds,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'answers_count': self.answers.count()
        }

class UserAnswer(db.Model):
    __tablename__ = 'user_answers'

    id = db.Column(db.Integer, primary_key=True)
    attempt_id = db.Column(db.Integer, db.ForeignKey('exam_attempts.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions_with_options.id'), nullable=False)
    selected_option = db.Column(db.String(1))  # A, B, C, or D
    is_marked_for_review = db.Column(db.Boolean, default=False)
    time_spent_seconds = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    attempt = db.relationship('ExamAttempt', back_populates='answers')
    question = db.relationship('QuestionWithOptions')

    __table_args__ = (
        db.UniqueConstraint('attempt_id', 'question_id', name='unique_attempt_question'),
        db.Index('idx_user_answers_attempt_id', 'attempt_id'),
    )

    def __repr__(self):
        return f'<UserAnswer {self.id} Attempt:{self.attempt_id} Question:{self.question_id}>'

    def to_dict(self):
        is_correct = False
        if self.selected_option and self.question:
            is_correct = self.selected_option == self.question.correct_option

        return {
            'id': self.id,
            'attempt_id': self.attempt_id,
            'question_id': self.question_id,
            'question_text': self.question.text if self.question else None,
            'selected_option': self.selected_option,
            'is_correct': is_correct,
            'is_marked_for_review': self.is_marked_for_review,
            'time_spent_seconds': self.time_spent_seconds,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
