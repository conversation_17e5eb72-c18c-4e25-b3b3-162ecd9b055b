"""
Direct database migration script that doesn't rely on Flask-SQLAlchemy.
This script directly connects to the database and executes SQL commands.
"""

import pymysql
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database configuration from config.py
DB_HOST = 'localhost'
DB_USER = 'root'
DB_PASSWORD = 'admin'
DB_NAME = 'jishu'

def get_db_connection():
    """Create a database connection"""
    try:
        connection = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}")
        raise

def execute_sql(connection, sql, params=None):
    """Execute SQL command and return results"""
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params or ())
            connection.commit()
            return cursor.fetchall()
    except Exception as e:
        connection.rollback()
        logger.error(f"Error executing SQL: {str(e)}")
        logger.error(f"SQL: {sql}")
        raise

def upgrade():
    """Update tables to work with the new structure"""
    try:
        logger.info("Starting database migration...")

        # Connect to the database
        connection = get_db_connection()

        # Create purchases table
        logger.info("Creating purchases table...")
        execute_sql(connection, '''
        CREATE TABLE IF NOT EXISTS purchases (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            subject_id INT NOT NULL,
            purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            max_retakes INT DEFAULT 3,
            retake_used INT DEFAULT 0,
            marks INT DEFAULT 4,
            negative_marks INT DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (subject_id) REFERENCES subjects(id)
        )
        ''')

        # Create payments table
        logger.info("Creating payments table...")
        execute_sql(connection, '''
        CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'INR',
            payment_method VARCHAR(50),
            transaction_id VARCHAR(100),
            status VARCHAR(20) DEFAULT 'pending',
            payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        ''')

        # Create payment_purchases table
        logger.info("Creating payment_purchases table...")
        execute_sql(connection, '''
        CREATE TABLE IF NOT EXISTS payment_purchases (
            payment_id INT NOT NULL,
            purchase_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (payment_id, purchase_id),
            FOREIGN KEY (payment_id) REFERENCES payments(id),
            FOREIGN KEY (purchase_id) REFERENCES purchases(id)
        )
        ''')

        # Check if exam_attempts table exists
        logger.info("Checking if exam_attempts table exists...")
        tables = execute_sql(connection, "SHOW TABLES LIKE 'exam_attempts'")

        if tables:
            # Alter exam_attempts table to use purchase_id
            logger.info("Altering exam_attempts table to use purchase_id...")
            try:
                execute_sql(connection, '''
                ALTER TABLE exam_attempts
                ADD COLUMN purchase_id INT,
                ADD CONSTRAINT fk_exam_attempts_purchase FOREIGN KEY (purchase_id) REFERENCES purchases(id)
                ''')

                # Create index on purchase_id
                execute_sql(connection, '''
                CREATE INDEX idx_exam_attempts_purchase_id ON exam_attempts(purchase_id)
                ''')

                logger.info("Successfully altered exam_attempts table.")
            except Exception as e:
                logger.warning(f"Error altering exam_attempts table: {str(e)}")
                logger.info("This may be because the changes were already applied.")
        else:
            logger.info("exam_attempts table doesn't exist yet. No need to alter it.")

        # Close the connection
        connection.close()

        logger.info("Migration completed successfully!")
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        raise

def downgrade():
    """Revert changes"""
    try:
        logger.info("Starting database downgrade...")

        # Connect to the database
        connection = get_db_connection()

        # Check if exam_attempts table exists
        logger.info("Checking if exam_attempts table exists...")
        tables = execute_sql(connection, "SHOW TABLES LIKE 'exam_attempts'")

        if tables:
            # Drop foreign key constraint in exam_attempts
            logger.info("Removing purchase_id from exam_attempts table...")
            try:
                # Get the constraint name
                constraints = execute_sql(connection, '''
                SELECT CONSTRAINT_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                WHERE TABLE_NAME = 'exam_attempts'
                AND COLUMN_NAME = 'purchase_id'
                AND CONSTRAINT_NAME != 'PRIMARY'
                AND TABLE_SCHEMA = %s
                ''', (DB_NAME,))

                if constraints:
                    constraint_name = constraints[0]['CONSTRAINT_NAME']
                    execute_sql(connection, f'''
                    ALTER TABLE exam_attempts
                    DROP FOREIGN KEY {constraint_name}
                    ''')

                # Drop the column
                execute_sql(connection, '''
                ALTER TABLE exam_attempts
                DROP COLUMN purchase_id
                ''')

                logger.info("Removed purchase_id from exam_attempts table.")
            except Exception as e:
                logger.warning(f"Error removing purchase_id from exam_attempts: {str(e)}")

        # Drop the new tables in reverse order
        logger.info("Dropping new tables...")
        try:
            execute_sql(connection, 'DROP TABLE IF EXISTS payment_purchases')
            execute_sql(connection, 'DROP TABLE IF EXISTS payments')
            execute_sql(connection, 'DROP TABLE IF EXISTS purchases')
            logger.info("Dropped new tables.")
        except Exception as e:
            logger.error(f"Error dropping tables: {str(e)}")
            raise

        # Close the connection
        connection.close()

        logger.info("Downgrade completed successfully!")
    except Exception as e:
        logger.error(f"Downgrade failed: {str(e)}")
        raise

if __name__ == "__main__":
    # Ask the user what to do
    action = input("Do you want to upgrade or downgrade the database? (upgrade/downgrade): ").strip().lower()

    if action == 'upgrade':
        upgrade()
    elif action == 'downgrade':
        downgrade()
    else:
        print("Invalid action. Please specify 'upgrade' or 'downgrade'.")
