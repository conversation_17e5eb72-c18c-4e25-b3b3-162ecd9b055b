# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 28ms
  [gap of 13ms]
generate_cxx_metadata completed in 45ms

# C/C++ build system timings
generate_cxx_metadata 40ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 17ms
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 60ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 44ms]
  create-invalidation-state 35ms
  [gap of 12ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 111ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 159ms]
  create-invalidation-state 52ms
  [gap of 20ms]
  write-metadata-json-to-file 29ms
  [gap of 52ms]
generate_cxx_metadata completed in 312ms

