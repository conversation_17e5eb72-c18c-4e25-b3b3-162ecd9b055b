import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { requestOTP, verifyOTP, clearError, resetOtpState } from '../../store/slices/authSlice';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  error: '#F44336',
};

const MobileLoginForm = () => {
  const [mobile, setMobile] = useState('');
  const [otp, setOtp] = useState('');
  const dispatch = useDispatch();
  const { isLoading, error, otpSent, mobile: storedMobile } = useSelector((state) => state.auth);

  // Use the stored mobile number from Redux if available
  useEffect(() => {
    if (storedMobile && !mobile) {
      setMobile(storedMobile);
    }
  }, [storedMobile]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // Show error alert if login fails
  useEffect(() => {
    if (error) {
      Alert.alert('Authentication Failed', error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  // Function to handle going back to mobile input
  const handleBack = () => {
    // Reset OTP state in Redux
    dispatch(resetOtpState());
  };

  const handleRequestOTP = () => {
    if (!mobile.trim() || mobile.length < 10) {
      Alert.alert('Validation Error', 'Please enter a valid 10-digit mobile number');
      return;
    }

    dispatch(requestOTP({ mobile }));
  };

  const handleVerifyOTP = () => {
    if (!otp.trim() || otp.length < 6) {
      Alert.alert('Validation Error', 'Please enter the 6-digit OTP');
      return;
    }

    dispatch(verifyOTP({ mobile, otp }));
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <Text style={styles.title}>Login to Jishu</Text>
      <Text style={styles.subtitle}>
        {otpSent
          ? 'Enter the OTP sent to your mobile'
          : 'Enter your mobile number to continue'}
      </Text>

      {!otpSent ? (
        // Mobile Number Input
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Mobile Number</Text>
          <View style={styles.mobileInputContainer}>
            <Text style={styles.countryCode}>+91</Text>
            <TextInput
              style={styles.mobileInput}
              placeholder="Enter 10-digit mobile number"
              placeholderTextColor={COLORS.textSecondary}
              value={mobile}
              onChangeText={setMobile}
              keyboardType="phone-pad"
              maxLength={10}
            />
            {mobile.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setMobile('')}
              >
                <Text style={styles.clearButtonText}>✕</Text>
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            style={styles.button}
            onPress={handleRequestOTP}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.buttonText}>Get OTP</Text>
            )}
          </TouchableOpacity>
        </View>
      ) : (
        // OTP Input
        <View style={styles.inputContainer}>
          <View style={styles.otpHeader}>
            <Text style={styles.label}>One-Time Password (OTP)</Text>
            <TouchableOpacity onPress={handleBack}>
              <Text style={styles.changeNumberText}>Change Number</Text>
            </TouchableOpacity>
          </View>

          <Text style={styles.mobileDisplay}>OTP sent to: +91 {mobile}</Text>

          <TextInput
            style={styles.input}
            placeholder="Enter 6-digit OTP"
            placeholderTextColor={COLORS.textSecondary}
            value={otp}
            onChangeText={setOtp}
            keyboardType="number-pad"
            maxLength={6}
          />

          <View style={styles.otpActions}>
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleRequestOTP}
              disabled={isLoading}
            >
              <Text style={styles.resendButtonText}>Resend OTP</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.button}
              onPress={handleVerifyOTP}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.buttonText}>Verify & Login</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}

     
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 24,
    backgroundColor: COLORS.card,
    borderRadius: 12,
    width: '100%',
    maxWidth: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 8,
  },
  mobileInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginBottom: 16,
  },
  countryCode: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRightWidth: 1,
    borderRightColor: COLORS.border,
    color: COLORS.text,
    fontWeight: '500',
  },
  mobileInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    color: COLORS.text,
  },
  clearButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    backgroundColor: COLORS.background,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: COLORS.text,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginBottom: 16,
  },
  button: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  otpActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resendButton: {
    padding: 8,
  },
  resendButtonText: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  otpHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  changeNumberText: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  mobileDisplay: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 16,
  },
  demoAccounts: {
    marginTop: 24,
    padding: 16,
    backgroundColor: COLORS.background,
    borderRadius: 8,
  },
  demoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 8,
  },
  demoText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
});

export default MobileLoginForm;
