# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 37ms
  [gap of 12ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 92ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 25ms
  [gap of 18ms]
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 29ms]
  create-invalidation-state 14ms
  [gap of 29ms]
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 14ms
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 52ms]
  create-invalidation-state 40ms
  [gap of 21ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 138ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 48ms]
  create-invalidation-state 37ms
  [gap of 22ms]
  write-metadata-json-to-file 37ms
generate_cxx_metadata completed in 145ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 42ms
  [gap of 12ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 102ms

