// Simple script to test the backend connection
const fetch = require('node-fetch');
const API_URL = 'http://localhost:5000';

async function testBackendConnection() {
  try {
    console.log(`Testing connection to backend at ${API_URL}/test...`);
    const response = await fetch(`${API_URL}/test`);
    const data = await response.json();
    console.log('Backend response:', data);
    console.log('Connection successful!');
  } catch (error) {
    console.error('Error connecting to backend:', error.message);
    console.log('\nPossible solutions:');
    console.log('1. Make sure the Flask backend is running');
    console.log('2. Check if the API_URL in config.js matches your backend server address');
    console.log('3. Ensure there are no firewall or network issues blocking the connection');
  }
}

testBackendConnection();
