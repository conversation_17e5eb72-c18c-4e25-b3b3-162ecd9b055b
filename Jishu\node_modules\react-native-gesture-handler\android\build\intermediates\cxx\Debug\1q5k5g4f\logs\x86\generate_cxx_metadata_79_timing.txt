# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 40ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 75ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 33ms]
  create-invalidation-state 65ms
  [gap of 22ms]
generate_cxx_metadata completed in 120ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 20ms
generate_cxx_metadata completed in 40ms

