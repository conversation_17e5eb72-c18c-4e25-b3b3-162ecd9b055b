"""Migration to update user_progress tables to work with the new database structure"""

import os
import sys
import pymysql
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the parent directory to sys.path to allow importing the app module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Use PyMySQL as the MySQL client
pymysql.install_as_MySQLdb()

# Import Flask app and db after setting up the path
from app import create_app, db

def upgrade():
    """Update tables to work with the new structure"""
    try:
        # Create the Flask app
        app = create_app()

        # Push an application context
        with app.app_context():
            logger.info("Starting database migration...")

            # Create the new tables first
            logger.info("Creating new tables: purchases, payments, payment_purchases...")

            # Create purchases table
            db.engine.execute('''
            CREATE TABLE IF NOT EXISTS purchases (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                subject_id INT NOT NULL,
                purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                max_retakes INT DEFAULT 3,
                retake_used INT DEFAULT 0,
                marks INT DEFAULT 4,
                negative_marks INT DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (subject_id) REFERENCES subjects(id)
            )
            ''')

            # Create payments table
            db.engine.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'INR',
                payment_method VARCHAR(50),
                transaction_id VARCHAR(100),
                status VARCHAR(20) DEFAULT 'pending',
                payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
            ''')

            # Create payment_purchases table
            db.engine.execute('''
            CREATE TABLE IF NOT EXISTS payment_purchases (
                payment_id INT NOT NULL,
                purchase_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (payment_id, purchase_id),
                FOREIGN KEY (payment_id) REFERENCES payments(id),
                FOREIGN KEY (purchase_id) REFERENCES purchases(id)
            )
            ''')

            # Alter exam_attempts table to use purchase_id
            logger.info("Altering exam_attempts table to use purchase_id...")
            try:
                db.engine.execute('''
                ALTER TABLE exam_attempts
                ADD COLUMN purchase_id INT,
                ADD CONSTRAINT fk_exam_attempts_purchase FOREIGN KEY (purchase_id) REFERENCES purchases(id)
                ''')

                # Create index on purchase_id
                db.engine.execute('''
                CREATE INDEX idx_exam_attempts_purchase_id ON exam_attempts(purchase_id)
                ''')

                logger.info("Successfully altered exam_attempts table.")
            except Exception as e:
                logger.warning(f"Error altering exam_attempts table: {str(e)}")
                logger.info("This may be because the changes were already applied or the table doesn't exist yet.")

            logger.info("Migration completed successfully!")
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        raise

def downgrade():
    """Revert changes"""
    try:
        # Create the Flask app
        app = create_app()

        # Push an application context
        with app.app_context():
            logger.info("Starting database downgrade...")

            # Drop the new tables in reverse order
            logger.info("Dropping new tables...")

            # Drop foreign key constraint in exam_attempts
            try:
                db.engine.execute('''
                ALTER TABLE exam_attempts
                DROP FOREIGN KEY fk_exam_attempts_purchase,
                DROP COLUMN purchase_id
                ''')
                logger.info("Removed purchase_id from exam_attempts table.")
            except Exception as e:
                logger.warning(f"Error removing purchase_id from exam_attempts: {str(e)}")

            # Drop the new tables
            try:
                db.engine.execute('DROP TABLE IF EXISTS payment_purchases')
                db.engine.execute('DROP TABLE IF EXISTS payments')
                db.engine.execute('DROP TABLE IF EXISTS purchases')
                logger.info("Dropped new tables.")
            except Exception as e:
                logger.error(f"Error dropping tables: {str(e)}")
                raise

            logger.info("Downgrade completed successfully!")
    except Exception as e:
        logger.error(f"Downgrade failed: {str(e)}")
        raise

if __name__ == "__main__":
    upgrade()
