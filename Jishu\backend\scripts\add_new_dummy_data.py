"""
<PERSON><PERSON><PERSON> to add initial dummy data to the database with the new payment structure.
"""

import os
import sys

# Add the parent directory to sys.path to allow importing the app module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app, db
from app.models.user import User
from app.models.exam import ExamCategory, Subject, QuestionWithOptions
from app.models.user_progress import ExamAttempt, UserAnswer
from app.models.payment import Payment, Purchase, PaymentPurchase
from datetime import datetime, timedelta, timezone
from decimal import Decimal
import random

def add_new_dummy_data():
    """Add dummy data to the database with the new payment structure"""
    app = create_app()
    with app.app_context():
        print("Adding dummy data to the database with the new payment structure...")

        # Check if data already exists
        if db.session.query(User).count() > 0:
            print("Users already exist in the database. Skipping user creation.")
            users = db.session.query(User).all()
        else:
            # Add users
            users = add_users()

        # Check if categories already exist
        if db.session.query(ExamCategory).count() > 0:
            print("Exam categories already exist in the database. Skipping category creation.")
            categories = db.session.query(ExamCategory).all()
            subjects = db.session.query(Subject).all()
        else:
            # Add exam categories and subjects
            categories, subjects = add_exam_categories_and_subjects()

        # Check if questions already exist
        if db.session.query(QuestionWithOptions).count() > 0:
            print("Questions already exist in the database. Skipping question creation.")
            questions = db.session.query(QuestionWithOptions).all()
        else:
            # Add questions
            questions = add_questions_with_options(subjects)

        # Check if purchases already exist
        if db.session.query(Purchase).count() > 0:
            print("Purchases already exist in the database. Skipping purchase creation.")
            purchases = db.session.query(Purchase).all()
        else:
            # Add purchases and payments
            purchases, payments = add_purchases_and_payments(users, subjects)

        # Check if exam attempts already exist
        if db.session.query(ExamAttempt).count() > 0:
            print("Exam attempts already exist in the database. Skipping attempt creation.")
            exam_attempts = db.session.query(ExamAttempt).all()
        else:
            # Add exam attempts and answers
            exam_attempts = add_exam_attempts_and_answers(purchases, questions)

        print("Dummy data added successfully!")

def add_users():
    """Add dummy users with realistic Indian names"""
    print("Adding users...")

    users = []

    # Admin user
    admin = User(
        mobile_number="9876543210",
        name="Rajesh Kumar",
        avatar="admin_avatar.jpg",
        role="admin",
        created_at=datetime.now(timezone.utc) - timedelta(days=90),
        last_login=datetime.now(timezone.utc),
        is_profile_complete=True
    )
    users.append(admin)

    # Student users with realistic Indian names and mobile numbers
    student_data = [
        {"mobile": "9876543211", "name": "Priya Sharma", "avatar": "student1.jpg"},
        {"mobile": "9876543212", "name": "Amit Patel", "avatar": "student2.jpg"},
        {"mobile": "9876543213", "name": "Sneha Gupta", "avatar": "student3.jpg"},
        {"mobile": "9876543214", "name": "Vikram Singh", "avatar": "student4.jpg"},
        {"mobile": "9876543215", "name": "Neha Verma", "avatar": "student5.jpg"},
        {"mobile": "9876543216", "name": "Arjun Reddy", "avatar": "student6.jpg"},
        {"mobile": "9876543217", "name": "Ananya Desai", "avatar": "student7.jpg"},
        {"mobile": "9876543218", "name": "Rahul Mehta", "avatar": "student8.jpg"},
        {"mobile": "9876543219", "name": "Pooja Iyer", "avatar": "student9.jpg"}
    ]

    for data in student_data:
        student = User(
            mobile_number=data["mobile"],
            name=data["name"],
            avatar=data["avatar"],
            role="student",
            created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 60)),
            last_login=datetime.now(timezone.utc) - timedelta(minutes=random.randint(5, 1440)),
            is_profile_complete=True
        )
        users.append(student)

    db.session.add_all(users)
    db.session.commit()
    print(f"Added {len(users)} users")
    return users

def add_exam_categories_and_subjects():
    """Add exam categories and subjects with realistic Indian exam names"""
    print("Adding exam categories and subjects...")

    categories = [
        ExamCategory(
            name="NEET",
            description="National Eligibility cum Entrance Test for Medical Admissions",
            icon="neet_icon.png"
        ),
        ExamCategory(
            name="JEE",
            description="Joint Entrance Examination for Engineering",
            icon="jee_icon.png"
        ),
        ExamCategory(
            name="GATE",
            description="Graduate Aptitude Test in Engineering",
            icon="gate_icon.png"
        )
    ]

    db.session.add_all(categories)
    db.session.commit()

    subjects = []

    # NEET subjects
    neet_subjects = [
        {"name": "Physics", "description": "NEET Physics - Mechanics, Thermodynamics, etc.", "duration": 60},
        {"name": "Chemistry", "description": "NEET Chemistry - Organic, Inorganic & Physical", "duration": 60},
        {"name": "Biology", "description": "NEET Biology - Zoology & Botany", "duration": 60}
    ]

    for subject_data in neet_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=f"{subject_data['name'].lower()}_icon.png",
            duration_minutes=subject_data["duration"],
            category_id=categories[0].id
        )
        subjects.append(subject)

    # JEE subjects
    jee_subjects = [
        {"name": "Mathematics", "description": "JEE Mathematics - Algebra, Calculus & Geometry", "duration": 60},
        {"name": "Physics", "description": "JEE Physics - Mechanics, Electromagnetism, etc.", "duration": 60},
        {"name": "Chemistry", "description": "JEE Chemistry - Physical, Organic & Inorganic", "duration": 60}
    ]

    for subject_data in jee_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=f"{subject_data['name'].lower()}_icon.png",
            duration_minutes=subject_data["duration"],
            category_id=categories[1].id
        )
        subjects.append(subject)

    # GATE subjects
    gate_subjects = [
        {"name": "Computer Science", "description": "GATE CS - Algorithms, OS, DBMS", "duration": 180},
        {"name": "Electronics", "description": "GATE ECE - Digital, Analog, Communications", "duration": 180}
    ]

    for subject_data in gate_subjects:
        subject = Subject(
            name=subject_data["name"],
            description=subject_data["description"],
            icon=f"{subject_data['name'].lower().replace(' ', '_')}_icon.png",
            duration_minutes=subject_data["duration"],
            category_id=categories[2].id
        )
        subjects.append(subject)

    db.session.add_all(subjects)
    db.session.commit()

    print(f"Added {len(categories)} categories and {len(subjects)} subjects")
    return categories, subjects

def add_questions_with_options(subjects):
    """Add realistic questions for each subject"""
    print("Adding questions with options...")

    questions = []

    # Sample questions for Physics
    physics_questions = [
        {
            "text": "A body of mass 2 kg is thrown vertically upward with a velocity of 20 m/s. What is its kinetic energy at the highest point?",
            "options": ["0 J", "200 J", "400 J", "800 J"],
            "correct": "A",
            "explanation": "At the highest point, vertical velocity becomes zero, hence kinetic energy is 0 J."
        },
        {
            "text": "Which of the following is the SI unit of pressure?",
            "options": ["Pascal", "Newton", "Joule", "Watt"],
            "correct": "A",
            "explanation": "Pascal (Pa) is the SI unit of pressure, defined as force per unit area (N/m²)."
        }
    ]

    # Sample questions for Chemistry
    chemistry_questions = [
        {
            "text": "What is the hybridization of carbon in methane (CH₄)?",
            "options": ["sp³", "sp²", "sp", "None of these"],
            "correct": "A",
            "explanation": "In methane, carbon forms four single bonds with hydrogen atoms, exhibiting sp³ hybridization."
        },
        {
            "text": "Which of the following is a strong acid?",
            "options": ["HCl", "CH₃COOH", "H₂CO₃", "NH₄OH"],
            "correct": "A",
            "explanation": "HCl (Hydrochloric acid) is a strong acid as it completely dissociates in water."
        }
    ]

    for subject in subjects:
        question_set = []
        if "Physics" in subject.name:
            question_set = physics_questions
        elif "Chemistry" in subject.name:
            question_set = chemistry_questions

        for q_data in question_set:
            question = QuestionWithOptions(
                subject_id=subject.id,
                text=q_data["text"],
                difficulty=random.choice(["easy", "medium", "hard"]),
                marks=4,
                negative_marks=1,
                option_a=q_data["options"][0],
                option_b=q_data["options"][1],
                option_c=q_data["options"][2],
                option_d=q_data["options"][3],
                correct_option=q_data["correct"],
                explanation=q_data["explanation"],
                created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30))
            )
            questions.append(question)

    db.session.add_all(questions)
    db.session.commit()

    print(f"Added {len(questions)} questions")
    return questions

def add_purchases_and_payments(users, subjects):
    """Add purchases and payments with the new structure"""
    print("Adding purchases and payments...")

    purchases = []
    payments = []
    payment_purchases = []

    # Skip admin user
    for user in users[1:]:
        # Each user has 1-2 payments
        num_payments = random.randint(1, 2)

        for _ in range(num_payments):
            # Create payment
            payment_date = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30))
            status = random.choice(["completed", "completed", "completed", "failed"])

            payment = Payment(
                user_id=user.id,
                amount=Decimal('0.00'),  # Will update after adding purchases
                currency="INR",
                payment_method=random.choice(["upi", "credit_card", "debit_card"]),
                transaction_id=f"TXN{random.randint(100000, 999999)}",
                status=status,
                payment_date=payment_date,
                created_at=payment_date,
                updated_at=payment_date + timedelta(minutes=random.randint(1, 10))
            )

            db.session.add(payment)
            db.session.flush()  # Get the payment ID

            payments.append(payment)

            # Calculate payment amount
            total_amount = Decimal('0.00')

            # Each payment covers 1-3 subjects
            selected_subjects = random.sample(subjects, random.randint(1, 3))

            for subject in selected_subjects:
                # Create purchase
                price = Decimal(str(random.randint(499, 1499)))
                total_amount += price

                purchase = Purchase(
                    user_id=user.id,
                    subject_id=subject.id,
                    purchase_date=payment_date,
                    max_retakes=3,
                    retake_used=random.randint(0, 2),
                    marks=4,
                    negative_marks=1,
                    created_at=payment_date,
                    updated_at=payment_date
                )

                db.session.add(purchase)
                db.session.flush()  # Get the purchase ID

                purchases.append(purchase)

                # Create payment_purchase association
                payment_purchase = PaymentPurchase(
                    payment_id=payment.id,
                    purchase_id=purchase.id,
                    amount=price,
                    created_at=payment_date
                )

                payment_purchases.append(payment_purchase)

            # Update payment amount
            payment.amount = total_amount

    db.session.add_all(payment_purchases)
    db.session.commit()

    print(f"Added {len(payments)} payments and {len(purchases)} purchases")
    return purchases, payments

def add_exam_attempts_and_answers(purchases, questions):
    """Add exam attempts and answers"""
    print("Adding exam attempts and answers...")

    exam_attempts = []
    user_answers = []

    for purchase in purchases:
        # Add 1-2 attempts for each purchase
        num_attempts = random.randint(1, min(2, purchase.retake_used + 1))

        for attempt_num in range(1, num_attempts + 1):
            # Get questions for this subject
            subject_questions = [q for q in questions if q.subject_id == purchase.subject_id]

            if not subject_questions:
                continue

            # Randomly select questions for this attempt
            num_questions = min(len(subject_questions), 10)
            selected_questions = random.sample(subject_questions, num_questions)

            # Calculate attempt statistics
            correct_answers = random.randint(0, num_questions)
            wrong_answers = random.randint(0, num_questions - correct_answers)
            unattempted = num_questions - correct_answers - wrong_answers

            # Calculate score using the marks from the purchase
            score = (correct_answers * purchase.marks) - (wrong_answers * purchase.negative_marks)

            # Create attempt
            started_at = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 10))
            time_taken = random.randint(15, 60) * 60  # 15-60 minutes in seconds

            attempt = ExamAttempt(
                user_id=purchase.user_id,
                purchase_id=purchase.id,
                attempt_number=attempt_num,
                score=score,
                total_questions=num_questions,
                correct_answers=correct_answers,
                wrong_answers=wrong_answers,
                unattempted=unattempted,
                time_taken_seconds=time_taken,
                started_at=started_at,
                completed_at=started_at + timedelta(seconds=time_taken)
            )

            db.session.add(attempt)
            db.session.flush()  # Get the attempt ID

            exam_attempts.append(attempt)

            # Add user answers for this attempt
            for i, question in enumerate(selected_questions):
                is_correct = i < correct_answers
                is_wrong = correct_answers <= i < (correct_answers + wrong_answers)
                is_unattempted = i >= (correct_answers + wrong_answers)

                if is_unattempted:
                    # No answer for unattempted questions
                    continue

                selected_option = question.correct_option if is_correct else random.choice([opt for opt in ["A", "B", "C", "D"] if opt != question.correct_option])
                is_marked_for_review = random.choice([True, False]) if random.random() < 0.2 else False

                answer = UserAnswer(
                    attempt_id=attempt.id,
                    question_id=question.id,
                    selected_option=selected_option,
                    is_marked_for_review=is_marked_for_review,
                    time_spent_seconds=random.randint(30, 180),
                    created_at=started_at + timedelta(seconds=random.randint(60, time_taken - 60))
                )

                user_answers.append(answer)

    db.session.add_all(user_answers)
    db.session.commit()

    print(f"Added {len(exam_attempts)} exam attempts and {len(user_answers)} user answers")
    return exam_attempts

if __name__ == "__main__":
    add_new_dummy_data()
